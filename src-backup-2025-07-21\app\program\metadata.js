export async function generateMetadata({ searchParams }) {
  const params = await searchParams;
  const destination = params?.destination || 'bali';
  
  const destinationData = {
    bali: {
      title: 'BALI Retreaty Jogi 2025 - Transformacyjne Podróże | BAKASANA',
      description: 'Odk<PERSON>j najlepsze retreaty jogi na Bali z certyfikowaną instruktorką Julią Jakubowicz. Ubud, Gili Air, tarasy ryżowe, świątynie hinduistyczne. Małe grupy, luksusowe hotele, autentyczne doświadczenia duchowe.',
      keywords: [
        'retreat jogi bali 2025',
        'ubud yoga retreat',
        'gili air joga',
        'tarasy ryżowe joga',
        'świątynie hinduistyczne bali',
        'julia jakubow<PERSON> bali',
        'transformacyjne retreaty bali',
        'duchowe podróże bali',
        'kelingking nusa penida',
        'bali joga wakacje'
      ],
      image: '/images/destinations/bali-hero.jpg'
    },
    srilanka: {
      title: 'SRI LANKA Retreaty Jogi 2025 - Perła Oceanu Indyjskiego | BAKASANA',
      description: 'Doświadcz magii Sri Lanki z retreatami jogi z Julią Jakubowicz. Sigiriya, Kandy, ayurveda, świątynie buddyjskie, Złota Świątynia. Autentyczne doświadczenia w sercu Oceanu Indyjskiego.',
      keywords: [
        'retreat jogi sri lanka 2025',
        'sigiriya yoga retreat',
        'kandy joga',
        'ayurveda sri lanka',
        'świątynie buddyjskie',
        'julia jakubowicz sri lanka',
        'złota świątynia sri lanka',
        'ocean indyjski joga',
        'transformacyjne podróże azja',
        'sri lanka wellness'
      ],
      image: '/images/destinations/sri-lanka-hero.jpg'
    }
  };

  const current = destinationData[destination] || destinationData.bali;

  return {
    metadataBase: new URL('https://bakasana-travel.blog'),
    title: current.title,
    description: current.description,
    keywords: current.keywords,
    
    openGraph: {
      title: current.title,
      description: current.description,
      url: `https://bakasana-travel.blog/program?destination=${destination}`,
      siteName: 'BAKASANA - Retreaty Jogi',
      images: [
        {
          url: `https://bakasana-travel.blog${current.image}`,
          width: 1200,
          height: 630,
          alt: `${current.title} - Retreat jogi`,
        }
      ],
      type: 'website',
      locale: 'pl_PL',
    },

    twitter: {
      card: 'summary_large_image',
      title: current.title,
      description: current.description,
      images: [`https://bakasana-travel.blog${current.image}`],
      creator: '@bakasana_travel',
      site: '@bakasana_travel',
    },

    alternates: {
      canonical: `https://bakasana-travel.blog/program?destination=${destination}`,
      languages: {
        'pl-PL': `https://bakasana-travel.blog/program?destination=${destination}`,
        'en-US': `https://bakasana-travel.blog/program?destination=${destination}&lang=en`,
      },
    },

    robots: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      yandex: process.env.YANDEX_VERIFICATION,
      bing: process.env.BING_VERIFICATION,
    },

    other: {
      'pinterest-rich-pins': 'true',
      'format-detection': 'telephone=yes',
      'mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'black-translucent',
    },
  };
}