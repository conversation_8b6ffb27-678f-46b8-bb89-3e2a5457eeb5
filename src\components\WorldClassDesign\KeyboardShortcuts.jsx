'use client';

import React, { useEffect, useState } from 'react';

/**
 * ⌨️ KEYBOARD SHORTCUTS - TOP 1% DESIGN FEATURE
 * "?" pokazuje wszystkie skróty, "ESC" zamyka modals
 * Inspirowane przez Linear.app, Notion, najlepsze SaaS
 */
const KeyboardShortcuts = ({ 
  enabled = true,
  showHelp = true,
  customShortcuts = {},
  className = '',
  ...props 
}) => {
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [pressedKeys, setPressedKeys] = useState(new Set());

  // Default shortcuts
  const defaultShortcuts = {
    '?': () => setShowHelpModal(true),
    'Escape': () => {
      setShowHelpModal(false);
      // Close any open modals
      const modals = document.querySelectorAll('[data-modal], .modal');
      modals.forEach(modal => {
        if (modal.style.display !== 'none') {
          modal.style.display = 'none';
        }
      });
    },
    'h': () => {
      // Go to home
      if (window.location.pathname !== '/') {
        window.location.href = '/';
      }
    },
    'b': () => {
      // Go to blog
      if (window.location.pathname !== '/blog') {
        window.location.href = '/blog';
      }
    },
    'c': () => {
      // Go to contact
      if (window.location.pathname !== '/kontakt') {
        window.location.href = '/kontakt';
      }
    },
    'p': () => {
      // Go to program
      if (window.location.pathname !== '/program') {
        window.location.href = '/program';
      }
    },
    'j': () => {
      // Scroll down
      window.scrollBy({ top: 200, behavior: 'smooth' });
    },
    'k': () => {
      // Scroll up
      window.scrollBy({ top: -200, behavior: 'smooth' });
    },
    'g g': () => {
      // Go to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    },
    'G': () => {
      // Go to bottom
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    },
    't': () => {
      // Toggle theme (if implemented)
      document.body.classList.toggle('dark-mode');
    },
    'f': () => {
      // Focus search (if exists)
      const searchInput = document.querySelector('input[type="search"], #search, .search-input');
      if (searchInput) {
        searchInput.focus();
      }
    },
    '/': () => {
      // Focus search
      const searchInput = document.querySelector('input[type="search"], #search, .search-input');
      if (searchInput) {
        searchInput.focus();
      }
    },
    'r': () => {
      // Refresh page
      window.location.reload();
    },
    'Backspace': () => {
      // Go back
      window.history.back();
    },
    'Alt+ArrowLeft': () => {
      // Go back
      window.history.back();
    },
    'Alt+ArrowRight': () => {
      // Go forward
      window.history.forward();
    },
    'Ctrl+Enter': () => {
      // Submit form (if focused)
      const activeElement = document.activeElement;
      if (activeElement && activeElement.form) {
        activeElement.form.submit();
      }
    },
  };

  const allShortcuts = { ...defaultShortcuts, ...customShortcuts };

  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (e) => {
      // Don't trigger shortcuts when typing in inputs
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.contentEditable === 'true') {
        return;
      }

      const key = e.key;
      const isModifierPressed = e.ctrlKey || e.altKey || e.metaKey;
      const keyCombo = [
        e.ctrlKey && 'Ctrl',
        e.altKey && 'Alt',
        e.metaKey && 'Meta',
        e.shiftKey && 'Shift',
        key
      ].filter(Boolean).join('+');

      // Handle key combinations
      if (isModifierPressed) {
        if (allShortcuts[keyCombo]) {
          e.preventDefault();
          allShortcuts[keyCombo]();
        }
        return;
      }

      // Handle single keys
      if (allShortcuts[key]) {
        e.preventDefault();
        allShortcuts[key]();
        return;
      }

      // Handle sequential keys (like "g g")
      setPressedKeys(prev => {
        const newKeys = new Set(prev);
        newKeys.add(key);
        
        // Check for sequential shortcuts
        const keySequence = Array.from(newKeys).join(' ');
        if (allShortcuts[keySequence]) {
          e.preventDefault();
          allShortcuts[keySequence]();
          return new Set(); // Clear pressed keys
        }
        
        // Clear keys after timeout
        setTimeout(() => {
          setPressedKeys(new Set());
        }, 1000);
        
        return newKeys;
      });
    };

    const handleKeyUp = (e) => {
      // Visual feedback for key presses
      const keyIndicator = document.querySelector('.key-indicator');
      if (keyIndicator) {
        keyIndicator.textContent = e.key;
        keyIndicator.style.opacity = '1';
        setTimeout(() => {
          keyIndicator.style.opacity = '0';
        }, 300);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [enabled, allShortcuts]);

  const shortcutGroups = {
    'Navigation': {
      'h': 'Go to Home',
      'b': 'Go to Blog',
      'c': 'Go to Contact',
      'p': 'Go to Program',
      'Backspace': 'Go Back',
      'Alt+←': 'Go Back',
      'Alt+→': 'Go Forward',
    },
    'Scrolling': {
      'j': 'Scroll Down',
      'k': 'Scroll Up',
      'g g': 'Go to Top',
      'G': 'Go to Bottom',
    },
    'Interface': {
      '?': 'Show Help',
      'Escape': 'Close Modal',
      'f': 'Focus Search',
      '/': 'Focus Search',
      't': 'Toggle Theme',
      'r': 'Refresh Page',
    },
    'Forms': {
      'Ctrl+Enter': 'Submit Form',
    },
  };

  return (
    <>
      {/* Key indicator */}
      <div
        className="key-indicator"
        style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '6px',
          fontSize: '14px',
          fontWeight: '500',
          fontFamily: 'monospace',
          zIndex: 9999,
          opacity: '0',
          transition: 'opacity 0.2s ease',
          pointerEvents: 'none',
        }}
      />

      {/* Help modal */}
      {showHelpModal && (
        <div
          className={`keyboard-shortcuts-modal ${className}`}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
            backdropFilter: 'blur(4px)',
          }}
          onClick={() => setShowHelpModal(false)}
          {...props}
        >
          <div
            style={{
              background: 'white',
              borderRadius: '12px',
              padding: '32px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '80vh',
              overflow: 'auto',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
              <h2 style={{ margin: 0, fontSize: '24px', fontWeight: '600', color: '#333' }}>
                Keyboard Shortcuts
              </h2>
              <button
                onClick={() => setShowHelpModal(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '24px',
                  cursor: 'pointer',
                  color: '#666',
                  padding: '4px',
                }}
              >
                ×
              </button>
            </div>

            <div style={{ display: 'grid', gap: '24px' }}>
              {Object.entries(shortcutGroups).map(([group, shortcuts]) => (
                <div key={group}>
                  <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: '600', color: '#7C9885' }}>
                    {group}
                  </h3>
                  <div style={{ display: 'grid', gap: '8px' }}>
                    {Object.entries(shortcuts).map(([key, description]) => (
                      <div
                        key={key}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '8px 0',
                          borderBottom: '1px solid var(--whisper)',
                        }}
                      >
                        <span style={{ fontSize: '14px', color: '#666' }}>{description}</span>
                        <kbd
                          style={{
                            background: '#f5f5f5',
                            color: '#333',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontFamily: 'monospace',
                            fontWeight: '500',
                            border: '1px solid #ddd',
                            minWidth: '40px',
                            textAlign: 'center',
                          }}
                        >
                          {key}
                        </kbd>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div style={{ marginTop: '24px', textAlign: 'center' }}>
              <p style={{ fontSize: '14px', color: '#666', margin: 0 }}>
                Press <kbd style={{ background: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>Escape</kbd> to close
              </p>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .keyboard-shortcuts-modal {
          font-family: var(--font-sans);
        }
        
        @media (max-width: 768px) {
          .keyboard-shortcuts-modal > div {
            padding: 24px !important;
            margin: 20px !important;
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .key-indicator {
            transition: none !important;
          }
        }
      `}</style>
    </>
  );
};

export default React.memo(KeyboardShortcuts);