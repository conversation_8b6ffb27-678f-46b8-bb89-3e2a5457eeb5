// Metadata export for Next.js
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, Clock, Users, Star, Check, ArrowRight, 
  Calendar, Phone, Mail, Instagram, Heart, Award
} from 'lucide-react';

export const metadata = {
  title: 'Retreaty Jogi Bali 2025 - Naj<PERSON><PERSON>ze Oferty z Julią Jakubowicz | BAKASANA',
  description: '🏆 Najlepsze retreaty jogi na Bali 2025 z certyfikowaną instruktorką Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐ 127 opinii. Rezerwuj teraz!',
  keywords: 'retreaty jogi bali 2025, ubud yoga retreat, gili air joga, julia jak<PERSON>, transformacyjne podróże, medytacja bali, ayurveda, najlepsze retreaty jogi',
  openGraph: {
    title: 'Retreaty Jogi Bali 2025 - Najlepsze Oferty | BAKASANA',
    description: '🏆 Najlepsze retreaty jogi na Bali z Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐ Rezerwuj teraz!',
    images: ['/images/og/retreaty-jogi-bali-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/retreaty-jogi-bali-2025',
  },
};

const RetreatyJogiBali2025 = () => {
  const features = [
    {
      icon: <Users className="w-5 h-5 text-temple" />,
      title: "Małe grupy",
      description: "Maksymalnie 12 osób - indywidualne podejście"
    },
    {
      icon: <Award className="w-5 h-5 text-temple" />,
      title: "Certyfikowana instruktorka",
      description: "Julia Jakubowicz - 200h YTT, fizjoterapeutka"
    },
    {
      icon: <MapPin className="w-5 h-5 text-temple" />,
      title: "Ekskluzywne lokalizacje",
      description: "Ubud, Gili Air, tarasy ryżowe, świątynie"
    },
    {
      icon: <Heart className="w-5 h-5 text-temple" />,
      title: "Transformacyjne doświadczenie",
      description: "Joga, medytacja, ayurveda, duchowa podróż"
    }
  ];

  const included = [
    "Zakwaterowanie w luksusowych hotelach 4-5*",
    "Wszystkie posiłki (vegetarian/vegan)",
    "Daily joga i medytacja",
    "Ayurveda masaże i terapie",
    "Zwiedzanie świątyń i tarasów ryżowych",
    "Transport lokalny",
    "Opieka instruktora i tłumacza 24/7",
    "Certyfikat ukończenia retreatu"
  ];

  const testimonials = [
    {
      name: "Anna Kowalska",
      text: "Niesamowite doświadczenie! Julia jest fantastyczną instruktorką, a organizacja na najwyższym poziomie. Bali to magiczne miejsce dla praktyki jogi.",
      rating: 5,
      location: "Warszawa"
    },
    {
      name: "Marcin Nowak", 
      text: "Retreat z BAKASANA to była transformująca podróż. Ubud i Gili Air to rajskie miejsca, a grupa była jak rodzina. Polecam każdemu!",
      rating: 5,
      location: "Kraków"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-temple/10 to-golden/10">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/destinations/bali-hero.webp"
            alt="Retreat jogi Bali 2025 - Ubud tarasy ryżowe"
            fill
            className="object-cover opacity-30"
            priority
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <Badge className="mb-4 bg-temple/20 text-temple border-temple/30">
            #1 Retreaty Jogi Bali 2025
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold text-charcoal mb-6">
            Najlepsze Retreaty Jogi <br />
            <span className="text-temple">na Bali 2025</span>
          </h1>
          
          <p className="text-xl text-wood mb-8 max-w-2xl mx-auto">
            Transformacyjne podróże z certyfikowaną instruktorką Julią Jakubowicz. 
            Ubud, Gili Air, małe grupy, luksusowe hotele. 4.9/5 ⭐ 127 opinii.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Rezerwuj Teraz
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
              asChild
            >
              <Link href="/program">
                Zobacz Program
              </Link>
            </Button>
          </div>
          
          <div className="mt-8 flex items-center justify-center gap-6 text-wood">
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-golden fill-golden" />
              <span className="font-semibold">4.9/5</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-temple" />
              <span>127 opinii</span>
            </div>
            <div className="flex items-center gap-2">
              <Award className="w-5 h-5 text-temple" />
              <span>Certyfikowana instruktorka</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Dlaczego BAKASANA to najlepszy wybór?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Odkryj dlaczego setki osób wybrały nasze retreaty jogi na Bali. 
              Jakość, profesjonalizm i niezapomniane doświadczenia.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-temple/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-charcoal mb-2">{feature.title}</h3>
                  <p className="text-sm text-wood">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Program Preview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-6">
                Program Retreatu Jogi Bali 2025
              </h2>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">7-14 dni transformacyjnej podróży</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <MapPin className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">Ubud (tarasy ryżowe) + Gili Air (plaże)</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">Maksymalnie 12 osób w grupie</span>
                </div>
              </div>
              
              <div className="space-y-3 mb-8">
                <h3 className="font-semibold text-charcoal">Co zawiera program:</h3>
                {included.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-temple flex-shrink-0" />
                    <span className="text-wood text-sm">{item}</span>
                  </div>
                ))}
              </div>
              
              <Button 
                className="bg-temple hover:bg-temple/90"
                asChild
              >
                <Link href="/program">
                  Zobacz Pełny Program
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/destinations/ubud-yoga.webp"
                    alt="Joga w Ubud - tarasy ryżowe"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/destinations/bali-temple.webp"
                    alt="Świątynie Bali - medytacja"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/destinations/gili-air-sunset.webp"
                    alt="Gili Air - sunset yoga"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/destinations/ayurveda-massage.webp"
                    alt="Ayurveda masaż Bali"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Co mówią uczestnicy retreatów?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Przeczytaj autentyczne opinie osób, które doświadczyły transformacji 
              podczas retreatów jogi na Bali z BAKASANA.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-golden fill-golden" />
                    ))}
                  </div>
                  <p className="text-wood mb-4 italic">"{testimonial.text}"</p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold text-charcoal">{testimonial.name}</p>
                      <p className="text-sm text-wood">{testimonial.location}</p>
                    </div>
                    <Badge variant="secondary" className="bg-temple/10 text-temple">
                      Zweryfikowana opinia
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-temple/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-4">
            Gotowy na transformującą podróż?
          </h2>
          <p className="text-wood mb-8 max-w-2xl mx-auto">
            Dołącz do nas na najlepszym retreecie jogi na Bali w 2025 roku. 
            Ograniczona liczba miejsc - zarezerwuj już dziś!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button 
              size="lg" 
              className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Rezerwuj Miejsce
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
              asChild
            >
              <Link href="/kontakt">
                Zadaj Pytanie
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-wood">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Instagram className="w-4 h-4" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "TouristTrip",
            "name": "Retreat Jogi Bali 2025 - BAKASANA",
            "description": "Najlepsze retreaty jogi na Bali z certyfikowaną instruktorką Julią Jakubowicz. Ubud, Gili Air, małe grupy, luksusowe hotele.",
            "url": "https://bakasana-travel.blog/retreaty-jogi-bali-2025",
            "image": "https://bakasana-travel.blog/images/destinations/bali-hero.webp",
            "startDate": "2025-03-01",
            "endDate": "2025-12-31",
            "offers": {
              "@type": "Offer",
              "price": "3400",
              "priceCurrency": "PLN",
              "availability": "https://schema.org/InStock",
              "url": "https://bakasana-travel.blog/rezerwacja"
            },
            "provider": {
              "@type": "TravelAgency",
              "name": "BAKASANA",
              "url": "https://bakasana-travel.blog"
            }
          })
        }}
      />
    </div>
  );
};

export default RetreatyJogiBali2025;