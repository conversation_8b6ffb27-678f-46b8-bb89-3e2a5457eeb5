'use client';

import React, { useRef, useEffect } from 'react';

/**
 * 🧲 MAGNETIC BUTTON - TOP 1% DESIGN FEATURE
 * Przyciski delika<PERSON> "przyciągają" kursor
 * Inspirowane przez Linear.app, Stripe, najlepsze SaaS
 */
const MagneticButton = ({ 
  children, 
  onClick,
  className = '',
  magneticStrength = 0.15,
  ...props 
}) => {
  const buttonRef = useRef(null);
  const contentRef = useRef(null);

  useEffect(() => {
    const button = buttonRef.current;
    const content = contentRef.current;
    
    if (!button || !content) return;

    const handleMouseMove = (e) => {
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      const distance = Math.sqrt(x * x + y * y);
      const maxDistance = Math.sqrt(rect.width * rect.width + rect.height * rect.height) / 2;
      
      if (distance < maxDistance * 1.5) {
        const translateX = x * magneticStrength;
        const translateY = y * magneticStrength;
        
        button.style.transform = `translate(${translateX}px, ${translateY}px)`;
        content.style.transform = `translate(${translateX * 0.3}px, ${translateY * 0.3}px)`;
      }
    };

    const handleMouseLeave = () => {
      button.style.transform = 'translate(0px, 0px)';
      content.style.transform = 'translate(0px, 0px)';
    };

    const handleMouseEnter = () => {
      button.style.cursor = 'pointer';
    };

    button.addEventListener('mousemove', handleMouseMove);
    button.addEventListener('mouseleave', handleMouseLeave);
    button.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      button.removeEventListener('mousemove', handleMouseMove);
      button.removeEventListener('mouseleave', handleMouseLeave);
      button.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, [magneticStrength]);

  return (
    <button
      ref={buttonRef}
      onClick={onClick}
      className={`
        relative overflow-hidden 
        transition-all duration-200 ease-out
        ${className}
        ${customClassName}
      `}
      style={{
        willChange: 'transform',
      }}
      {...props}
    >
      <div 
        ref={contentRef}
        className="relative z-10 transition-transform duration-200 ease-out"
        style={{
          willChange: 'transform',
        }}
      >
        {children}
      </div>
      
      {/* Subtle hover effect */}
      <div className="absolute inset-0 bg-black/[0.02] opacity-0 transition-opacity duration-200 pointer-events-none group-hover:opacity-100" />
    </button>
  );
};

export default React.memo(MagneticButton);