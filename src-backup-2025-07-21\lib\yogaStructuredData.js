// SEO Structured Data for Yoga Retreats in Bali & Sri Lanka

export const getRetreatStructuredData = (retreat) => {
  return {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": retreat.title,
    "description": retreat.description,
    "startDate": retreat.startDate,
    "endDate": retreat.endDate,
    "location": {
      "@type": "Place",
      "name": retreat.location,
      "address": {
        "@type": "PostalAddress",
        "addressCountry": retreat.location.includes('Bali') ? 'ID' : 'LK',
        "addressRegion": retreat.location
      }
    },
    "organizer": {
      "@type": "Person",
      "name": "<PERSON>",
      "description": "Certyfikowana instruktorka jogi z 10-letnim doświadczeniem",
      "knowsAbout": ["Yoga", "Meditation", "Wellness", "Bali", "Sri Lanka"]
    },
    "offers": {
      "@type": "Offer",
      "price": retreat.price?.replace('€', ''),
      "priceCurrency": "EUR",
      "availability": "https://schema.org/InStock",
      "url": `https://bakasana.pl/program/${retreat.id}`
    },
    "image": retreat.imageUrl,
    "keywords": [
      "yoga retreat",
      "Bali yoga",
      "Sri Lanka yoga",
      "meditation retreat",
      "wellness vacation",
      "transformational travel"
    ]
  }
}

export const getOrganizationStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "BAKASANA",
    "description": "Transformacyjne retreaty jogi na Bali i Sri Lance z Julią Jakubowicz",
    "url": "https://bakasana.pl",
    "logo": "https://bakasana.pl/images/logo/bakasana-logo.webp",
    "sameAs": [
      "https://instagram.com/bakasana_retreats",
      "https://facebook.com/bakasana.retreats"
    ],
    "founder": {
      "@type": "Person",
      "name": "Julia Jakubowicz",
      "jobTitle": "Certyfikowana Instruktorka Jogi",
      "description": "Specjalistka w zakresie transformacyjnych retreatów jogi na Bali i Sri Lance"
    },
    "areaServed": ["Poland", "Europe"],
    "serviceType": [
      "Yoga Retreats",
      "Meditation Workshops", 
      "Wellness Travel",
      "Spiritual Journeys"
    ]
  }
}

export const getTravelGuideStructuredData = (destination) => {
  const destinations = {
    bali: {
      name: "Bali, Indonezja",
      description: "Duchowe serce Azji - idealne miejsce na retreaty jogi",
      attractions: ["Ubud", "Gili Air", "Canggu", "Świątynie hinduistyczne"],
      climate: "Tropikalny",
      bestTimeToVisit: "Maj-September"
    },
    srilanka: {
      name: "Sri Lanka",
      description: "Perła Oceanu Indyjskiego - kolebka buddyzmu i ayurvedy",
      attractions: ["Sigiriya", "Kandy", "Ella", "Galle"],
      climate: "Tropikalny",
      bestTimeToVisit: "Grudzień-Marzec"
    }
  }

  const dest = destinations[destination]
  if (!dest) return null

  return {
    "@context": "https://schema.org",
    "@type": "TravelGuide",
    "name": `Przewodnik po retreatach jogi - ${dest.name}`,
    "description": dest.description,
    "about": {
      "@type": "Place",
      "name": dest.name,
      "description": dest.description
    },
    "touristAttraction": dest.attractions.map(attraction => ({
      "@type": "TouristAttraction",
      "name": attraction
    })),
    "author": {
      "@type": "Person",
      "name": "Julia Jakubowicz",
      "jobTitle": "Ekspert retreatów jogi w Azji"
    }
  }
}

export const getYogaInstructorStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "Julia Jakubowicz",
    "jobTitle": "Certyfikowana Instruktorka Jogi",
    "description": "Specjalistka w zakresie transformacyjnych retreatów jogi na Bali i Sri Lance z 10-letnim doświadczeniem",
    "knowsAbout": [
      "Hatha Yoga",
      "Vinyasa Flow", 
      "Meditation",
      "Yoga Philosophy",
      "Bali Culture",
      "Sri Lanka Traditions",
      "Ayurveda",
      "Wellness Travel"
    ],
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "Yoga School Rishikesh, India"
    },
    "hasOccupation": {
      "@type": "Occupation",
      "name": "Yoga Instructor",
      "occupationLocation": {
        "@type": "Place",
        "name": "Bali, Sri Lanka, Poland"
      }
    },
    "offers": {
      "@type": "Service",
      "name": "Yoga Retreats",
      "description": "Transformacyjne retreaty jogi w Azji",
      "areaServed": ["Bali", "Sri Lanka"],
      "serviceType": "Wellness & Spiritual Travel"
    }
  }
}

export const getFAQStructuredData = (faqs) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}