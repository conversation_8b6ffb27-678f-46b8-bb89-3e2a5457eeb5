'use client';

import React, { useState } from 'react';
import { 
  DocumentTextIcon, 
  MapIcon, 
  CurrencyDollarIcon, 
  CloudIcon,
  CameraIcon,
  HeartIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const TravelGuide = ({ destination, className = '' }) => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: InformationCircleIcon },
    { id: 'practical', label: 'Practical Info', icon: DocumentTextIcon },
    { id: 'culture', label: 'Culture', icon: HeartIcon },
    { id: 'weather', label: 'Weather', icon: CloudIcon },
    { id: 'budget', label: 'Budget', icon: CurrencyDollarIcon },
    { id: 'photos', label: 'Gallery', icon: CameraIcon }
  ];

  const practicalInfo = {
    visa: {
      required: false,
      duration: '30 days visa-free',
      note: 'Valid for Polish citizens'
    },
    currency: {
      local: 'Indonesian Rupiah (IDR)',
      rate: '1 USD = 15,000 IDR',
      cards: 'Widely accepted in tourist areas'
    },
    transport: {
      airport: 'Ngurah Rai International Airport (DPS)',
      local: 'Scooter rental, taxis, private drivers',
      tips: 'Download Grab app for easy transport'
    },
    health: {
      vaccinations: 'No required vaccinations',
      insurance: 'Travel insurance recommended',
      medical: 'Good healthcare in tourist areas'
    }
  };

  const culturalInfo = {
    religion: {
      primary: 'Hindu-Balinese',
      temples: 'Respect dress codes, remove shoes',
      ceremonies: 'Colorful festivals year-round'
    },
    customs: {
      greetings: 'Namaste with palms together',
      dress: 'Modest clothing for temples',
      tipping: '10-15% in restaurants'
    },
    language: {
      official: 'Indonesian (Bahasa Indonesia)',
      local: 'Balinese',
      english: 'Widely spoken in tourist areas'
    }
  };

  const weatherInfo = {
    seasons: {
      dry: 'April - September (best for yoga)',
      wet: 'October - March (fewer crowds)'
    },
    temperature: {
      average: '24-30°C year-round',
      humidity: '60-80%',
      uv: 'Very high - sunscreen essential'
    },
    clothing: {
      light: 'Cotton and linen clothes',
      yoga: 'Breathable yoga wear',
      rain: 'Light rain jacket for wet season'
    }
  };

  const budgetInfo = {
    accommodation: {
      budget: '$20-40/night',
      mid: '$40-100/night',
      luxury: '$100+/night'
    },
    food: {
      local: '$2-5/meal',
      tourist: '$5-15/meal',
      fine: '$15+/meal'
    },
    activities: {
      yoga: '$10-20/class',
      massage: '$10-30/session',
      tours: '$20-50/day'
    }
  };

  const TabContent = ({ tab }) => {
    switch (tab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="font-cormorant text-lg font-medium text-charcoal mb-3">
                About {destination?.name || 'Bali'}
              </h3>
              <p className="text-stone leading-relaxed">
                Experience the spiritual heart of Indonesia where ancient Hindu traditions blend 
                seamlessly with modern wellness practices. This island paradise offers the perfect 
                setting for deepening your yoga practice while exploring magnificent temples, 
                lush rice terraces, and pristine beaches.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-sanctuary p-4 rounded">
                <h4 className="font-medium text-charcoal mb-2">Best For</h4>
                <ul className="text-sm text-stone space-y-1">
                  <li>• Yoga and meditation retreats</li>
                  <li>• Cultural immersion</li>
                  <li>• Spiritual growth</li>
                  <li>• Wellness treatments</li>
                </ul>
              </div>
              
              <div className="bg-sanctuary p-4 rounded">
                <h4 className="font-medium text-charcoal mb-2">Highlights</h4>
                <ul className="text-sm text-stone space-y-1">
                  <li>• Ancient Hindu temples</li>
                  <li>• Ubud rice terraces</li>
                  <li>• Traditional art villages</li>
                  <li>• Sacred water temples</li>
                </ul>
              </div>
            </div>
          </div>
        );
      
      case 'practical':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-charcoal mb-3 flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-sage-green" />
                  Visa Requirements
                </h4>
                <div className="bg-sanctuary p-4 rounded">
                  <p className="text-sm text-stone mb-2">
                    <strong>Polish citizens:</strong> {practicalInfo.visa.duration}
                  </p>
                  <p className="text-xs text-stone">{practicalInfo.visa.note}</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-charcoal mb-3 flex items-center gap-2">
                  <CurrencyDollarIcon className="h-5 w-5 text-temple-gold" />
                  Currency
                </h4>
                <div className="bg-sanctuary p-4 rounded">
                  <p className="text-sm text-stone mb-1">{practicalInfo.currency.local}</p>
                  <p className="text-xs text-stone mb-2">{practicalInfo.currency.rate}</p>
                  <p className="text-xs text-stone">{practicalInfo.currency.cards}</p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-charcoal mb-3 flex items-center gap-2">
                <MapIcon className="h-5 w-5 text-ocean-blue" />
                Transportation
              </h4>
              <div className="bg-sanctuary p-4 rounded">
                <p className="text-sm text-stone mb-2">
                  <strong>Airport:</strong> {practicalInfo.transport.airport}
                </p>
                <p className="text-sm text-stone mb-2">
                  <strong>Local transport:</strong> {practicalInfo.transport.local}
                </p>
                <p className="text-xs text-stone">{practicalInfo.transport.tips}</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-charcoal mb-3 flex items-center gap-2">
                <ExclamationTriangleIcon className="h-5 w-5 text-temple-gold" />
                Health & Safety
              </h4>
              <div className="bg-sanctuary p-4 rounded">
                <p className="text-sm text-stone mb-2">
                  <strong>Vaccinations:</strong> {practicalInfo.health.vaccinations}
                </p>
                <p className="text-sm text-stone mb-2">
                  <strong>Insurance:</strong> {practicalInfo.health.insurance}
                </p>
                <p className="text-xs text-stone">{practicalInfo.health.medical}</p>
              </div>
            </div>
          </div>
        );
      
      case 'culture':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium text-charcoal mb-3">Religion & Spirituality</h4>
              <div className="bg-sanctuary p-4 rounded">
                <p className="text-sm text-stone mb-2">
                  <strong>Primary religion:</strong> {culturalInfo.religion.primary}
                </p>
                <p className="text-sm text-stone mb-2">
                  <strong>Temple etiquette:</strong> {culturalInfo.religion.temples}
                </p>
                <p className="text-xs text-stone">{culturalInfo.religion.ceremonies}</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-charcoal mb-3">Local Customs</h4>
              <div className="bg-sanctuary p-4 rounded space-y-3">
                <div>
                  <p className="text-sm text-stone">
                    <strong>Greetings:</strong> {culturalInfo.customs.greetings}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-stone">
                    <strong>Dress code:</strong> {culturalInfo.customs.dress}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-stone">
                    <strong>Tipping:</strong> {culturalInfo.customs.tipping}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-charcoal mb-3">Language</h4>
              <div className="bg-sanctuary p-4 rounded">
                <p className="text-sm text-stone mb-2">
                  <strong>Official:</strong> {culturalInfo.language.official}
                </p>
                <p className="text-sm text-stone mb-2">
                  <strong>Local:</strong> {culturalInfo.language.local}
                </p>
                <p className="text-xs text-stone">{culturalInfo.language.english}</p>
              </div>
            </div>
          </div>
        );
      
      case 'weather':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-charcoal mb-3">Seasons</h4>
                <div className="bg-sanctuary p-4 rounded">
                  <p className="text-sm text-stone mb-2">
                    <strong>Dry season:</strong> {weatherInfo.seasons.dry}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Wet season:</strong> {weatherInfo.seasons.wet}
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-charcoal mb-3">Temperature</h4>
                <div className="bg-sanctuary p-4 rounded">
                  <p className="text-sm text-stone mb-2">
                    <strong>Average:</strong> {weatherInfo.temperature.average}
                  </p>
                  <p className="text-sm text-stone mb-2">
                    <strong>Humidity:</strong> {weatherInfo.temperature.humidity}
                  </p>
                  <p className="text-xs text-stone">{weatherInfo.temperature.uv}</p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-charcoal mb-3">What to Pack</h4>
              <div className="bg-sanctuary p-4 rounded">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-charcoal mb-1">Light clothing</p>
                    <p className="text-xs text-stone">{weatherInfo.clothing.light}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-charcoal mb-1">Yoga wear</p>
                    <p className="text-xs text-stone">{weatherInfo.clothing.yoga}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-charcoal mb-1">Rain gear</p>
                    <p className="text-xs text-stone">{weatherInfo.clothing.rain}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'budget':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-charcoal mb-3">Accommodation</h4>
                <div className="bg-sanctuary p-4 rounded space-y-2">
                  <p className="text-sm text-stone">
                    <strong>Budget:</strong> {budgetInfo.accommodation.budget}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Mid-range:</strong> {budgetInfo.accommodation.mid}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Luxury:</strong> {budgetInfo.accommodation.luxury}
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-charcoal mb-3">Food</h4>
                <div className="bg-sanctuary p-4 rounded space-y-2">
                  <p className="text-sm text-stone">
                    <strong>Local meals:</strong> {budgetInfo.food.local}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Tourist spots:</strong> {budgetInfo.food.tourist}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Fine dining:</strong> {budgetInfo.food.fine}
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-charcoal mb-3">Activities</h4>
                <div className="bg-sanctuary p-4 rounded space-y-2">
                  <p className="text-sm text-stone">
                    <strong>Yoga class:</strong> {budgetInfo.activities.yoga}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Massage:</strong> {budgetInfo.activities.massage}
                  </p>
                  <p className="text-sm text-stone">
                    <strong>Day tours:</strong> {budgetInfo.activities.tours}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-temple-gold/10 p-4 rounded">
              <h4 className="font-medium text-charcoal mb-2">Budget Tips</h4>
              <ul className="text-sm text-stone space-y-1">
                <li>• Eat at local warungs for authentic and cheap meals</li>
                <li>• Use local transport instead of tourist taxis</li>
                <li>• Book accommodation in advance for better rates</li>
                <li>• Negotiate prices at markets and for long-term stays</li>
              </ul>
            </div>
          </div>
        );
      
      case 'photos':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((index) => (
                <div key={index} className="aspect-square bg-sanctuary rounded overflow-hidden">
                  <div className="w-full h-full flex items-center justify-center text-stone">
                    <CameraIcon className="h-8 w-8" />
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center">
              <button className="bg-temple-gold text-white px-6 py-2 rounded hover:bg-temple-gold/90 transition-colors">
                View Full Gallery
              </button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={`bg-white rounded-none shadow-lg ${className}`}>
      {/* Tab Navigation */}
      <div className="border-b border-stone-light overflow-x-auto">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center gap-2 px-4 py-3 text-sm font-medium whitespace-nowrap
                transition-colors border-b-2
                ${activeTab === tab.id
                  ? 'border-temple-gold text-temple-gold'
                  : 'border-transparent text-stone hover:text-charcoal hover:border-stone-light'
                }
              `}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <TabContent tab={activeTab} />
      </div>
    </div>
  );
};

export default TravelGuide;