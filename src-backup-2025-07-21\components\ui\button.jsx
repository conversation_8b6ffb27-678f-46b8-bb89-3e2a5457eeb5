'use client';
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-secondary font-light transition-all duration-normal focus-visible:outline-none focus-visible:shadow-focus disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        // BAKASANA Unified Buttons - Using design tokens
        primary: "bg-transparent border border-charcoal text-charcoal hover:bg-enterprise-brown hover:text-sanctuary hover:border-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        secondary: "bg-transparent border border-stone text-stone hover:bg-stone hover:text-sanctuary hover:border-stone font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        accent: "bg-transparent border border-enterprise-brown text-enterprise-brown hover:bg-enterprise-brown hover:text-sanctuary hover:border-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        ghost: "bg-transparent text-charcoal hover:bg-whisper hover:text-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        // Legacy variants for compatibility
        outline: "bg-transparent border border-stone/20 text-charcoal hover:bg-whisper hover:text-enterprise-brown transition-all duration-normal",
        hero: "bg-transparent border border-stone/10 text-charcoal hover:bg-whisper hover:text-enterprise-brown transition-all duration-normal",
        // Extended warm & friendly variants
        peach: "bg-transparent border border-warm-peach text-warm-peach hover:bg-warm-peach hover:text-sanctuary hover:border-warm-peach font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        coral: "bg-transparent border border-friendly-coral text-friendly-coral hover:bg-friendly-coral hover:text-sanctuary hover:border-friendly-coral font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        sage: "bg-transparent border border-soft-sage text-soft-sage hover:bg-soft-sage hover:text-sanctuary hover:border-soft-sage font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        terracotta: "bg-transparent border border-terracotta text-terracotta hover:bg-terracotta hover:text-sanctuary hover:border-terracotta font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
      },
      size: {
        sm: "px-8 py-3 text-small",
        default: "px-12 py-4 text-small",
        lg: "px-16 py-5 text-caption",
        icon: "h-9 w-9 px-0",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

const Button = React.forwardRef(({ 
  className, 
  variant, 
  size, 
  asChild = false, 
  ...props 
}, ref) => {
  const Comp = asChild ? Slot : "button"
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

export { Button, buttonVariants }
