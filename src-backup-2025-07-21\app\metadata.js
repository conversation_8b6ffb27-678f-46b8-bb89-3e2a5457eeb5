// Metadata utilities for blog posts and other content

export function generateBlogMetadata({
  title,
  excerpt,
  tags = [],
  imageUrl,
  slug,
  author = '<PERSON>',
  date
}) {
  const baseUrl = 'https://bakasana-travel.blog';
  const fullTitle = `${title} | BAKASANA - Retreaty Jogi Bali`;
  const description = excerpt || 'Odkryj transformacyjną moc jogi na Bali z Julią Jakubowicz';
  const url = `${baseUrl}/blog/${slug}`;
  const image = imageUrl ? `${baseUrl}${imageUrl}` : `${baseUrl}/og-image.jpg`;

  return {
    title: fullTitle,
    description,
    keywords: tags.join(', '),
    authors: [{ name: author }],
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: 'BAKASANA - Retreaty Jogi Bali',
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'pl_PL',
      type: 'article',
      publishedTime: date,
      authors: [author],
      tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [image],
      creator: '@bakasana_yoga',
    },
    alternates: {
      canonical: url,
    },
    robots: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  };
}

export function generateBlogStructuredData({
  title,
  excerpt,
  date,
  slug,
  tags = [],
  imageUrl,
  author = 'Julia Jakubowicz',
  dateModified
}) {
  const baseUrl = 'https://bakasana-travel.blog';
  const url = `${baseUrl}/blog/${slug}`;
  const image = imageUrl ? `${baseUrl}${imageUrl}` : `${baseUrl}/og-image.jpg`;

  return {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": title,
    "description": excerpt,
    "image": {
      "@type": "ImageObject",
      "url": image,
      "width": 1200,
      "height": 630
    },
    "author": {
      "@type": "Person",
      "name": author,
      "url": `${baseUrl}/o-mnie`,
      "jobTitle": "Instruktorka Jogi",
      "worksFor": {
        "@type": "Organization",
        "name": "BAKASANA"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "BAKASANA - Retreaty Jogi Bali",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/apple-touch-icon.png`,
        "width": 180,
        "height": 180
      },
      "url": baseUrl
    },
    "datePublished": date,
    "dateModified": dateModified || date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": url
    },
    "url": url,
    "keywords": tags.join(', '),
    "articleSection": "Joga i Podróże",
    "inLanguage": "pl-PL",
    "about": [
      {
        "@type": "Thing",
        "name": "Joga"
      },
      {
        "@type": "Place",
        "name": "Bali"
      }
    ]
  };
}

// General page metadata generator
export function generatePageMetadata({
  title,
  description,
  keywords = [],
  imageUrl,
  path = '',
  type = 'website'
}) {
  const baseUrl = 'https://bakasana-travel.blog';
  const fullTitle = title.includes('BAKASANA') ? title : `${title} | BAKASANA - Retreaty Jogi Bali`;
  const url = `${baseUrl}${path}`;
  const image = imageUrl ? `${baseUrl}${imageUrl}` : `${baseUrl}/og-image.jpg`;

  return {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: 'BAKASANA - Retreaty Jogi Bali',
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'pl_PL',
      type,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [image],
      creator: '@bakasana_yoga',
    },
    alternates: {
      canonical: url,
    },
    robots: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  };
}

// Alias for generatePageMetadata for backward compatibility
export const generateMetadata = generatePageMetadata;

// Generate structured data for general pages
export function generateStructuredData({
  title,
  description,
  type = 'WebPage',
  path = ''
}) {
  const baseUrl = 'https://bakasana-travel.blog';
  const url = `${baseUrl}${path}`;

  return {
    "@context": "https://schema.org",
    "@type": type,
    "name": title,
    "description": description,
    "url": url,
    "isPartOf": {
      "@type": "WebSite",
      "name": "BAKASANA - Retreaty Jogi Bali",
      "url": baseUrl
    },
    "inLanguage": "pl-PL"
  };
}
