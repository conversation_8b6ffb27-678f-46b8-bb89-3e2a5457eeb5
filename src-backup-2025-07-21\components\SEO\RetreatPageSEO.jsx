'use client';

import { generateEventSchema, generateProductSchema, generateFAQSchema } from '@/lib/structuredDataManager';
import { SEO_CONFIG } from '@/lib/seoManager';

/**
 * 🧘‍♀️ RETREAT PAGE SEO COMPONENT
 * 
 * Specialized SEO optimization for retreat pages:
 * - Event schema for retreat dates
 * - Product schema for retreat packages
 * - FAQ schema for common questions
 * - Enhanced Open Graph for social sharing
 * - Local business schema for destinations
 */

const RetreatPageSEO = ({ retreat, faqs = [], reviews = [] }) => {
  // Generate Event schema for retreat
  const eventSchema = generateEventSchema({
    id: retreat.id,
    name: retreat.name,
    description: retreat.description,
    startDate: retreat.startDate,
    endDate: retreat.endDate,
    location: {
      name: retreat.location.name,
      city: retreat.location.city,
      country: retreat.location.country,
      coordinates: retreat.location.coordinates
    },
    image: retreat.image,
    url: retreat.url,
    price: retreat.price,
    bookingOpenDate: retreat.bookingOpenDate,
    maxParticipants: retreat.maxParticipants,
    availableSpots: retreat.availableSpots
  });
  
  // Generate Product schema for retreat package
  const productSchema = generateProductSchema({
    name: retreat.name,
    description: retreat.description,
    image: retreat.image,
    url: retreat.url,
    price: retreat.price,
    rating: retreat.rating
  });
  
  // Generate FAQ schema if FAQs provided
  const faqSchema = faqs.length > 0 ? generateFAQSchema(faqs) : null;
  
  // Generate aggregated review schema
  const aggregateRatingSchema = reviews.length > 0 ? {
    '@context': 'https://schema.org',
    '@type': 'AggregateRating',
    itemReviewed: {
      '@type': 'Event',
      name: retreat.name
    },
    ratingValue: reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length,
    reviewCount: reviews.length,
    bestRating: 5,
    worstRating: 1
  } : null;
  
  // Generate Place schema for retreat location
  const placeSchema = {
    '@context': 'https://schema.org',
    '@type': 'TouristDestination',
    name: retreat.location.name,
    description: `Beautiful ${retreat.location.name} - perfect destination for yoga retreats and spiritual journeys`,
    address: {
      '@type': 'PostalAddress',
      addressLocality: retreat.location.city,
      addressCountry: retreat.location.country
    },
    geo: retreat.location.coordinates && {
      '@type': 'GeoCoordinates',
      latitude: retreat.location.coordinates.lat,
      longitude: retreat.location.coordinates.lng
    },
    image: retreat.location.image || retreat.image,
    touristType: ['Wellness Tourist', 'Spiritual Seeker', 'Yoga Practitioner'],
    includesAttraction: [
      {
        '@type': 'TouristAttraction',
        name: 'Yoga Sessions',
        description: 'Daily yoga practice in beautiful natural settings'
      },
      {
        '@type': 'TouristAttraction', 
        name: 'Meditation',
        description: 'Guided meditation sessions and mindfulness practices'
      },
      {
        '@type': 'TouristAttraction',
        name: 'Cultural Exploration',
        description: 'Visit local temples, markets, and cultural sites'
      }
    ]
  };
  
  // Generate enhanced Open Graph data
  const enhancedOGData = {
    title: `${retreat.name} | BAKASANA Yoga Retreats`,
    description: retreat.description,
    image: `${SEO_CONFIG.siteUrl}${retreat.image}`,
    url: `${SEO_CONFIG.siteUrl}${retreat.url}`,
    type: 'product',
    site_name: SEO_CONFIG.siteName,
    locale: SEO_CONFIG.locale,
    // Product-specific OG tags
    'product:price:amount': retreat.price,
    'product:price:currency': 'PLN',
    'product:availability': retreat.availableSpots > 0 ? 'in stock' : 'out of stock',
    'product:condition': 'new',
    'product:retailer': SEO_CONFIG.organization.name,
    // Event-specific OG tags
    'event:start_time': retreat.startDate,
    'event:end_time': retreat.endDate,
    'event:location': retreat.location.name
  };
  
  // Generate Twitter Card data
  const twitterCardData = {
    card: 'summary_large_image',
    site: SEO_CONFIG.twitterHandle,
    creator: SEO_CONFIG.twitterHandle,
    title: retreat.name.length > 70 ? retreat.name.substring(0, 67) + '...' : retreat.name,
    description: retreat.description.length > 200 ? retreat.description.substring(0, 197) + '...' : retreat.description,
    image: `${SEO_CONFIG.siteUrl}${retreat.image}`,
    // Product-specific Twitter tags
    'twitter:label1': 'Cena',
    'twitter:data1': `${retreat.price} PLN`,
    'twitter:label2': 'Dostępne miejsca',
    'twitter:data2': retreat.availableSpots.toString()
  };
  
  return (
    <>
      {/* Enhanced Open Graph Meta Tags */}
      {Object.entries(enhancedOGData).map(([property, content]) => (
        <meta key={property} property={`og:${property}`} content={content} />
      ))}
      
      {/* Enhanced Twitter Card Meta Tags */}
      {Object.entries(twitterCardData).map(([name, content]) => (
        <meta key={name} name={name} content={content} />
      ))}
      
      {/* Additional Meta Tags for Retreats */}
      <meta name="geo.region" content={retreat.location.country} />
      <meta name="geo.placename" content={retreat.location.city} />
      {retreat.location.coordinates && (
        <>
          <meta name="geo.position" content={`${retreat.location.coordinates.lat};${retreat.location.coordinates.lng}`} />
          <meta name="ICBM" content={`${retreat.location.coordinates.lat}, ${retreat.location.coordinates.lng}`} />
        </>
      )}
      
      {/* Event Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(eventSchema)
        }}
      />
      
      {/* Product Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(productSchema)
        }}
      />
      
      {/* Place Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(placeSchema)
        }}
      />
      
      {/* FAQ Schema */}
      {faqSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(faqSchema)
          }}
        />
      )}
      
      {/* Aggregate Rating Schema */}
      {aggregateRatingSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(aggregateRatingSchema)
          }}
        />
      )}
      
      {/* Individual Review Schemas */}
      {reviews.map((review, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Review',
              itemReviewed: {
                '@type': 'Event',
                name: retreat.name
              },
              reviewRating: {
                '@type': 'Rating',
                ratingValue: review.rating,
                bestRating: 5,
                worstRating: 1
              },
              author: {
                '@type': 'Person',
                name: review.author
              },
              reviewBody: review.text,
              datePublished: review.date
            })
          }}
        />
      ))}
      
      {/* Preload critical retreat images */}
      <link rel="preload" as="image" href={`${SEO_CONFIG.siteUrl}${retreat.image}`} />
      {retreat.gallery && retreat.gallery.slice(0, 3).map((image, index) => (
        <link key={index} rel="preload" as="image" href={`${SEO_CONFIG.siteUrl}${image}`} />
      ))}
    </>
  );
};

export default RetreatPageSEO;
