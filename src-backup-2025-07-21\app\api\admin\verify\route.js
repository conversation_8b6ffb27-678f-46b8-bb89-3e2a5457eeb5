// src/app/api/admin/verify/route.js
import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export async function POST(request) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Brak tokenu autoryzacji' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Usuń "Bearer "

    try {
      // Weryfikuj token JWT
      const decoded = jwt.verify(token, JWT_SECRET, {
        issuer: 'bakasana-travel-admin',
        audience: 'bakasana-travel-app'
      });

      // Sprawdź czy token nie jest zbyt stary (dodatkowa walidacja)
      const tokenAge = Date.now() - decoded.loginTime;
      const maxAge = 24 * 60 * 60 * 1000; // 24 godziny

      if (tokenAge > maxAge) {
        return NextResponse.json(
          { success: false, error: 'Token wygasł' },
          { status: 401 }
        );
      }

      // Sprawdź rolę
      if (decoded.role !== 'admin') {
        return NextResponse.json(
          { success: false, error: 'Niewystarczające uprawnienia' },
          { status: 403 }
        );
      }

      return NextResponse.json({
        success: true,
        valid: true,
        user: {
          role: decoded.role,
          loginTime: decoded.loginTime,
          ip: decoded.ip
        },
        expiresAt: decoded.exp * 1000 // Konwertuj na milisekundy
      });

    } catch (jwtError) {
      console.warn('Invalid JWT token:', jwtError.message);
      
      return NextResponse.json(
        { success: false, error: 'Nieprawidłowy token' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Token verification error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd weryfikacji tokenu',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint dla sprawdzenia statusu
export async function GET() {
  return NextResponse.json({
    message: 'Admin token verification endpoint is working',
    timestamp: new Date().toISOString()
  });
}
