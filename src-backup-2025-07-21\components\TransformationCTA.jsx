'use client';

import React from 'react';
import Link from 'next/link';

const TransformationCTA = ({ className = '' }) => {
  return (
    <section className={`py-20 bg-gradient-to-b from-sanctuary to-whisper ${className}`}>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Decorative line */}
          <div className="w-16 h-px bg-temple-gold mx-auto mb-8 opacity-60"></div>
          
          <h2 className="text-4xl md:text-5xl font-cormorant font-light text-charcoal mb-6 leading-tight">
            Rozpocznij swoją transformację
          </h2>
          
          <p className="text-lg text-charcoal-light leading-relaxed mb-12 max-w-2xl mx-auto font-light">
            Skontaktuj się ze mną i odkryj głębię praktyki jogi - w przestrzeni fizycznej 
            na retreatach lub w intymnej atmos<PERSON><PERSON> zajęć online.
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link 
              href="/retreaty" 
              className="group inline-flex items-center px-8 py-3 bg-transparent border border-temple-gold text-temple-gold hover:bg-temple-gold hover:text-sanctuary transition-all duration-300 font-light tracking-wider text-sm uppercase hover:shadow-lg"
            >
              Zobacz retreaty
              <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
            
            <Link 
              href="/zajecia-online" 
              className="group inline-flex items-center px-8 py-3 bg-transparent border border-temple-gold text-temple-gold hover:bg-temple-gold hover:text-sanctuary transition-all duration-300 font-light tracking-wider text-sm uppercase hover:shadow-lg"
            >
              Zajęcia online
              <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
            
            <Link 
              href="/kontakt" 
              className="group inline-flex items-center px-8 py-3 bg-temple-gold border border-temple-gold text-sanctuary hover:bg-golden-amber hover:border-golden-amber transition-all duration-300 font-medium tracking-wider text-sm uppercase shadow-lg hover:shadow-xl hover:scale-105"
            >
              Skontaktuj się
              <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
          
          {/* Decorative line */}
          <div className="w-16 h-px bg-temple-gold mx-auto mt-12 opacity-60"></div>
        </div>
      </div>
    </section>
  );
};

export default TransformationCTA;