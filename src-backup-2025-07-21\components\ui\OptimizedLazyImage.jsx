/**
 * 🖼️ BAKASANA - OPTIMIZED LAZY IMAGE COMPONENT
 * 
 * High-performance image loading with:
 * - Progressive enhancement
 * - Blur-to-sharp transition
 * - WebP/AVIF support
 * - Intersection Observer
 * - Error handling
 * - Accessibility features
 * 
 * Usage:
 * <OptimizedLazyImage
 *   src="/images/hero.webp"
 *   alt="Bali sunset yoga"
 *   width={800}
 *   height={600}
 *   priority={true}
 * />
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useIntersectionObserver } from '@/hooks/useAdvancedAnimations';
import { cn } from '@/lib/utils';

const OptimizedLazyImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 85,
  placeholder = 'blur',
  blurDataURL,
  sizes = '100vw',
  objectFit = 'cover',
  objectPosition = 'center',
  onLoad,
  onError,
  ...props
}) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState('');
  const imgRef = useRef(null);
  
  // Intersection Observer for lazy loading
  const { entries, observe } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px 0px',
  });
  
  const isInView = entries.some(entry => entry.isIntersecting);
  
  // Generate placeholder blur data URL
  const generateBlurDataURL = (width, height) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;
    
    // Create gradient placeholder
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, 'rgba(253, 252, 248, 0.8)');
    gradient.addColorStop(0.5, 'rgba(184, 147, 92, 0.1)');
    gradient.addColorStop(1, 'rgba(253, 252, 248, 0.8)');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    return canvas.toDataURL('image/jpeg', 0.1);
  };
  
  // Initialize intersection observer
  useEffect(() => {
    if (imgRef.current) {
      observe(imgRef.current);
    }
  }, [observe]);
  
  // Load image when in view or priority
  useEffect(() => {
    if ((priority || isInView) && src && !currentSrc) {
      setCurrentSrc(src);
    }
  }, [isInView, priority, src, currentSrc]);
  
  // Handle image load
  const handleLoad = (e) => {
    setLoaded(true);
    setError(false);
    onLoad?.(e);
  };
  
  // Handle image error
  const handleError = (e) => {
    setError(true);
    setLoaded(false);
    onError?.(e);
  };
  
  const placeholderDataURL = blurDataURL || generateBlurDataURL(40, 30);
  
  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        'bg-whisper',
        className
      )}
      style={{ width, height }}
      {...props}
    >
      {/* Placeholder/Blur background */}
      {placeholder === 'blur' && (
        <div
          className={cn(
            'absolute inset-0 z-0',
            'bg-cover bg-center bg-no-repeat',
            'transition-opacity duration-300',
            loaded ? 'opacity-0' : 'opacity-100'
          )}
          style={{
            backgroundImage: `url(${placeholderDataURL})`,
            filter: 'blur(20px)',
            transform: 'scale(1.1)',
          }}
        />
      )}
      
      {/* Shimmer loading animation */}
      {!loaded && !error && (
        <div className="absolute inset-0 z-10">
          <div className="w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer bg-[length:200%_100%]" />
        </div>
      )}
      
      {/* Main image */}
      {currentSrc && (
        <img
          src={currentSrc}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'absolute inset-0 z-20 w-full h-full',
            'transition-all duration-300 ease-out',
            loaded ? 'opacity-100' : 'opacity-0'
          )}
          style={{
            objectFit,
            objectPosition,
            transform: loaded ? 'scale(1)' : 'scale(1.05)',
          }}
        />
      )}
      
      {/* Error state */}
      {error && (
        <div className="absolute inset-0 z-30 flex items-center justify-center bg-rice">
          <div className="text-center text-stone">
            <svg
              className="mx-auto h-12 w-12 mb-2 text-stone-light"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm font-light">Nie udało się wczytać obrazu</p>
          </div>
        </div>
      )}
      
      {/* Loading indicator */}
      {currentSrc && !loaded && !error && (
        <div className="absolute inset-0 z-30 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-temple-gold border-t-transparent rectangular animate-spin" />
        </div>
      )}
    </div>
  );
};

export default OptimizedLazyImage;