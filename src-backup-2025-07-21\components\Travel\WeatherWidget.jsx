'use client';

import React, { useState, useEffect } from 'react';
import { 
  SunIcon, 
  CloudIcon, 
  CloudRainIcon, 
  EyeIcon,
  WindIcon,
  SunIcon as SunriseIcon,
  MoonIcon,
  DropletIcon,
  ThermometerIcon
} from 'lucide-react';

const WeatherWidget = ({ 
  destination, 
  compact = false,
  className = '' 
}) => {
  const [weather, setWeather] = useState(null);
  const [forecast, setForecast] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Mock weather data for demonstration
  const mockWeatherData = {
    bali: {
      current: {
        temperature: 28,
        condition: 'partly-cloudy',
        humidity: 78,
        windSpeed: 12,
        visibility: 8,
        uvIndex: 7,
        sunrise: '06:15',
        sunset: '18:45',
        feelsLike: 31,
        pressure: 1012
      },
      forecast: [
        { day: 'Today', high: 30, low: 24, condition: 'partly-cloudy', precipitation: 20 },
        { day: 'Tomorrow', high: 32, low: 25, condition: 'sunny', precipitation: 10 },
        { day: 'Wednesday', high: 29, low: 23, condition: 'rainy', precipitation: 85 },
        { day: 'Thursday', high: 31, low: 24, condition: 'partly-cloudy', precipitation: 35 },
        { day: 'Friday', high: 33, low: 26, condition: 'sunny', precipitation: 5 }
      ],
      location: 'Ubud, Bali'
    },
    'sri-lanka': {
      current: {
        temperature: 26,
        condition: 'sunny',
        humidity: 72,
        windSpeed: 8,
        visibility: 10,
        uvIndex: 6,
        sunrise: '06:00',
        sunset: '18:30',
        feelsLike: 29,
        pressure: 1015
      },
      forecast: [
        { day: 'Today', high: 28, low: 22, condition: 'sunny', precipitation: 5 },
        { day: 'Tomorrow', high: 30, low: 23, condition: 'partly-cloudy', precipitation: 15 },
        { day: 'Wednesday', high: 27, low: 21, condition: 'rainy', precipitation: 75 },
        { day: 'Thursday', high: 29, low: 22, condition: 'partly-cloudy', precipitation: 25 },
        { day: 'Friday', high: 31, low: 24, condition: 'sunny', precipitation: 10 }
      ],
      location: 'Colombo, Sri Lanka'
    }
  };

  useEffect(() => {
    const fetchWeatherData = async () => {
      setLoading(true);
      
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const destinationKey = destination?.toLowerCase().includes('bali') ? 'bali' : 'sri-lanka';
        const data = mockWeatherData[destinationKey] || mockWeatherData['bali'];
        
        setWeather(data.current);
        setForecast(data.forecast);
        setError(null);
      } catch (err) {
        setError('Failed to fetch weather data');
        console.error('Weather fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, [destination]);

  const getWeatherIcon = (condition, size = 'w-6 h-6') => {
    switch (condition) {
      case 'sunny':
        return <SunIcon className={`${size} text-yellow-500`} />;
      case 'partly-cloudy':
        return <CloudIcon className={`${size} text-gray-500`} />;
      case 'rainy':
        return <CloudRainIcon className={`${size} text-blue-500`} />;
      case 'cloudy':
        return <CloudIcon className={`${size} text-gray-600`} />;
      default:
        return <SunIcon className={`${size} text-yellow-500`} />;
    }
  };

  const getConditionText = (condition) => {
    switch (condition) {
      case 'sunny':
        return 'Sunny';
      case 'partly-cloudy':
        return 'Partly Cloudy';
      case 'rainy':
        return 'Rainy';
      case 'cloudy':
        return 'Cloudy';
      default:
        return 'Clear';
    }
  };

  const getUVLevel = (uvIndex) => {
    if (uvIndex <= 2) return { level: 'Low', color: 'text-green-600' };
    if (uvIndex <= 5) return { level: 'Moderate', color: 'text-yellow-600' };
    if (uvIndex <= 7) return { level: 'High', color: 'text-orange-600' };
    if (uvIndex <= 10) return { level: 'Very High', color: 'text-temple-gold' };
    return { level: 'Extreme', color: 'text-purple-600' };
  };

  if (loading) {
    return (
      <div className={`bg-white rectangular p-4 shadow-sm animate-pulse ${className}`}>
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-stone-light rectangular" />
          <div className="flex-1">
            <div className="h-4 bg-stone-light rounded w-3/4 mb-2" />
            <div className="h-3 bg-stone-light rounded w-1/2" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rectangular p-4 shadow-sm ${className}`}>
        <div className="flex items-center gap-2 text-temple-gold">
          <CloudIcon className="w-5 h-5" />
          <span className="text-sm">{error}</span>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`bg-white rectangular p-4 shadow-sm ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getWeatherIcon(weather.condition, 'w-10 h-10')}
            <div>
              <div className="text-2xl font-bold text-charcoal">
                {weather.temperature}°C
              </div>
              <div className="text-sm text-stone">
                {getConditionText(weather.condition)}
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-stone mb-1">
              {destination || 'Bali'}
            </div>
            <div className="text-xs text-stone">
              Feels like {weather.feelsLike}°C
            </div>
          </div>
        </div>
      </div>
    );
  }

  const uvInfo = getUVLevel(weather.uvIndex);

  return (
    <div className={`bg-white rectangular shadow-sm overflow-hidden ${className}`}>
      {/* Current Weather */}
      <div className="p-6 bg-gradient-to-br from-sanctuary to-whisper">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-cormorant font-medium text-charcoal">
              Current Weather
            </h3>
            <p className="text-sm text-stone">{destination || 'Bali'}</p>
          </div>
          <div className="text-right">
            <div className="text-xs text-stone">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {getWeatherIcon(weather.condition, 'w-16 h-16')}
            <div>
              <div className="text-4xl font-bold text-charcoal">
                {weather.temperature}°C
              </div>
              <div className="text-stone">
                {getConditionText(weather.condition)}
              </div>
              <div className="text-sm text-stone">
                Feels like {weather.feelsLike}°C
              </div>
            </div>
          </div>
          
          <div className="text-right space-y-1">
            <div className="flex items-center gap-1 text-sm text-stone">
              <DropletIcon className="w-4 h-4" />
              {weather.humidity}%
            </div>
            <div className="flex items-center gap-1 text-sm text-stone">
              <WindIcon className="w-4 h-4" />
              {weather.windSpeed} km/h
            </div>
            <div className="flex items-center gap-1 text-sm text-stone">
              <EyeIcon className="w-4 h-4" />
              {weather.visibility} km
            </div>
          </div>
        </div>
      </div>

      {/* Weather Details */}
      <div className="p-6 border-b border-stone-light">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <SunriseIcon className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
            <div className="text-sm text-stone mb-1">Sunrise</div>
            <div className="font-medium text-charcoal">{weather.sunrise}</div>
          </div>
          
          <div className="text-center">
            <MoonIcon className="w-6 h-6 text-gray-500 mx-auto mb-2" />
            <div className="text-sm text-stone mb-1">Sunset</div>
            <div className="font-medium text-charcoal">{weather.sunset}</div>
          </div>
          
          <div className="text-center">
            <ThermometerIcon className="w-6 h-6 text-temple-gold mx-auto mb-2" />
            <div className="text-sm text-stone mb-1">UV Index</div>
            <div className={`font-medium ${uvInfo.color}`}>
              {weather.uvIndex} ({uvInfo.level})
            </div>
          </div>
          
          <div className="text-center">
            <svg className="w-6 h-6 text-blue-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
            <div className="text-sm text-stone mb-1">Pressure</div>
            <div className="font-medium text-charcoal">{weather.pressure} hPa</div>
          </div>
        </div>
      </div>

      {/* 5-Day Forecast */}
      <div className="p-6">
        <h4 className="font-cormorant font-medium text-charcoal mb-4">
          5-Day Forecast
        </h4>
        
        <div className="space-y-3">
          {forecast.map((day, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b border-stone-light last:border-b-0">
              <div className="flex items-center gap-3">
                <div className="w-16 text-sm text-stone font-medium">
                  {day.day}
                </div>
                {getWeatherIcon(day.condition, 'w-8 h-8')}
                <div className="text-sm text-stone">
                  {getConditionText(day.condition)}
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1 text-sm text-stone">
                  <DropletIcon className="w-3 h-3" />
                  {day.precipitation}%
                </div>
                <div className="text-right">
                  <span className="font-medium text-charcoal">{day.high}°</span>
                  <span className="text-stone mx-1">/</span>
                  <span className="text-stone">{day.low}°</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Travel Tips */}
      <div className="p-6 bg-sage-green/5 border-t border-stone-light">
        <h4 className="font-cormorant font-medium text-charcoal mb-3">
          Travel Tips
        </h4>
        
        <div className="space-y-2 text-sm text-stone">
          {weather.uvIndex > 7 && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rectangular" />
              <span>High UV - use sunscreen and protective clothing</span>
            </div>
          )}
          
          {weather.humidity > 80 && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rectangular" />
              <span>High humidity - stay hydrated and take breaks</span>
            </div>
          )}
          
          {forecast[0].precipitation > 70 && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-600 rectangular" />
              <span>Rain expected - bring waterproof gear</span>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rectangular" />
            <span>Perfect weather for outdoor yoga sessions</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeatherWidget;