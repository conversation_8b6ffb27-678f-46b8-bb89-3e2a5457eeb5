'use client';

import { useEffect } from 'react';

export default function MetaTags({
  title,
  description,
  image,
  url,
  type = 'website',
  keywords = [],
}) {
  const siteName = process.env.NEXT_PUBLIC_SITE_NAME || 'Wycieczki Bali';
  const siteDescription = process.env.NEXT_PUBLIC_SITE_DESCRIPTION || 'Odkryj p<PERSON>ę<PERSON> z nami';
  const siteImage = image || process.env.NEXT_PUBLIC_SITE_IMAGE_URL || '/og-image.jpg';
  const siteUrl = url || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const siteKeywords = keywords.length > 0 ? keywords.join(', ') : 'bali, podróże, wycieczki, indonezja, blog podróżniczy';

  useEffect(() => {
    // Aktualizacja tytułu dokumentu
    document.title = title ? `${title} | ${siteName}` : siteName;
    
    // Aktualizacja meta tagów
    const metaTags = {
      'description': description || siteDescription,
      'keywords': siteKeywords,
      
      // Open Graph / Facebook
      'og:type': type,
      'og:url': siteUrl,
      'og:title': title || siteName,
      'og:description': description || siteDescription,
      'og:image': siteImage,
      'og:site_name': siteName,
      
      // Twitter
      'twitter:card': 'summary_large_image',
      'twitter:url': siteUrl,
      'twitter:title': title || siteName,
      'twitter:description': description || siteDescription,
      'twitter:image': siteImage,
    };
    
    // Usuń istniejące meta tagi
    document.querySelectorAll('meta').forEach(meta => {
      const name = meta.getAttribute('name');
      const property = meta.getAttribute('property');
      if (name && metaTags[name] || property && metaTags[property]) {
        meta.remove();
      }
    });
    
    // Dodaj zaktualizowane meta tagi
    Object.entries(metaTags).forEach(([key, value]) => {
      const meta = document.createElement('meta');
      if (key.startsWith('og:') || key.startsWith('twitter:')) {
        meta.setAttribute('property', key);
      } else {
        meta.setAttribute('name', key);
      }
      meta.setAttribute('content', value);
      document.head.appendChild(meta);
    });
    
    // Aktualizacja kanonicznego URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', siteUrl);
  }, [title, description, image, url, type, siteKeywords]);

  return null;
}