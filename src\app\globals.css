@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import unified design tokens - SINGLE SOURCE OF TRUTH */
@import url('../styles/design-tokens.css');

/* Import nowych stylów Bakasana */
@import url('./bakasana-visuals.css');

/* Import ujednoliconego systemu designu */
@import url('../styles/unified-system.css');

/* Import hero styles */
@import url('../styles/hero.css');

/* Import navbar styles */
@import url('../styles/navbar.css');

/* ===== ACCESSIBILITY - SKIP LINKS ===== */

.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 0; /* BAKASANA RULE: Zero border-radius */
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== OLD MONEY ANIMATIONS ===== */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

/* ===== WHATSAPP BUTTON CRITICAL CSS ===== */
.whatsapp-elegant {
  background-color: var(--enterprise-brown);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-elegant:hover {
  background-color: rgba(var(--enterprise-brown-rgb), 0.9);
  transform: scale(1.05);
}

.whatsapp-float {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
  z-index: 50; /* Optimized: Below cookie banner but above content */
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

/* Ensure WhatsApp button doesn't conflict with cookie banner */
.whatsapp-float {
  margin-bottom: env(safe-area-inset-bottom, 0);
}

.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

/* =============================================
   🏛️ BAKASANA - ENTERPRISE 11/10 DESIGN SYSTEM
   Ultra-minimalist perfection meets spiritual elegance
   ============================================= */

/* OPTIMIZED FONT LOADING - Fonts loaded via Next.js font optimization system */
/* @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400&family=Inter:wght@300;400&display=swap'); */

:root {
  /* ===== LEGACY SPACING SYSTEM - Use design-tokens.css for new components ===== */
  --container-max: 1200px;
  --section-padding: 7.5rem 0;     /* 120px section padding */
  --element-breathing: 8%;          /* 8% side margins */
  --card-internal: 3rem;            /* 48px - Unified card padding */
  --micro-spacing: 2rem;            /* 32px - Micro spacing */
  --nano-spacing: 0.75rem;          /* 12px - Nano spacing */
  --hero-spacing: 7.5rem;           /* 120px - Hero section spacing */
  --whisper-spacing: 11.25rem;      /* 180px - Ultra-minimal sections */

  /* ===== RESPONSIVE BREAKPOINTS - Synchronized with Tailwind ===== */
  --mobile: 480px;    /* xs - aligned with Tailwind */
  --tablet: 768px;    /* sm - aligned with Tailwind */
  --desktop: 1024px;  /* md - aligned with Tailwind */
  --large: 1440px;    /* lg - aligned with Tailwind */
  --ultra: 1920px;    /* xl - aligned with Tailwind */

  /* ===== LEGACY COLOR DEFINITIONS - MIGRATED TO design-tokens.css ===== */
  /* Use design-tokens.css variables for new components */
  /* These are kept for backward compatibility only */

  /* ===== UNIFIED ANIMATION SYSTEM ===== */
  /* Consistent timing for all interactions */
  --duration-instant: 0.15s;    /* Micro-interactions */
  --duration-fast: 0.2s;        /* Quick feedback */
  --duration-normal: 0.3s;      /* Standard transitions */
  --duration-slow: 0.5s;        /* Complex animations */
  --duration-entrance: 0.8s;    /* Page/component entrance */

  /* Refined easing functions */
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);        /* Standard smooth */
  --ease-spring: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Gentle spring */
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Playful bounce */
  --ease-gentle: cubic-bezier(0.25, 0.1, 0.25, 1);    /* Ultra-smooth */

  /* Hover effect standards */
  --hover-lift: translateY(-2px);
  --hover-scale: scale(1.02);
  --hover-opacity: 0.8;
  
  /* Subtle black & white accents */
  --pure-white: var(--pure-white);        /* Clean white for contrast */
  --soft-black: var(--soft-black);        /* Soft black for emphasis */
  --charcoal-light: var(--charcoal-light);    /* Lighter charcoal */
  --stone-light: var(--stone-light);       /* Lighter stone */
  
  /* Nature touches - very subtle */
  --sage-whisper: #A8B5A8;      /* Barely visible green */
  --ocean-mist: #B8C5D1;        /* Barely visible blue */

  /* Extended warm & friendly colors */
  --cream: var(--cream);             /* Friendly cream */
  --warm-peach: var(--warm-peach);        /* Ciepły brzoskwiniowy */
  --soft-sage: var(--soft-sage);         /* Miękki szałwiowy */
  --terracotta: var(--terracotta);        /* Ciepła terakota */
  --blush: var(--blush);             /* Delikatny róż */
  --friendly-coral: var(--friendly-coral);    /* Przyjazny koral */
  --calm-lavender: var(--calm-lavender);     /* Spokojny lawendowy */

  /* Transparencies */
  --glass-nav: rgba(var(--sanctuary-rgb), 0.95);
  --subtle-shadow: rgba(26, 24, 22, 0.04);
  --hover-overlay: rgba(26, 24, 22, 0.08);

  /* ===== TYPOGRAPHY SCALE - WARM MINIMAL HIERARCHY ===== */
  /* Font families - z ciepłem i ludzkością */
  --font-primary: 'Montserrat', 'Open Sans', sans-serif;  /* przyjazna, czytelna */
  --font-secondary: 'Lato', 'Source Sans Pro', sans-serif;
  --font-accent: 'Caveat', cursive;  /* dla akcentów - odręczny charakter */

  /* Typography scale - Expanded for whisper aestheticies */
  --text-xs: 10px;              /* Ultra-small for whisper elements */
  --text-sm: 12px;              /* Small details */
  --text-base: 14px;            /* Body text - smaller for elegance */
  --text-lg: 16px;              /* Slightly larger body */
  --text-xl: 20px;              /* Subtitles */
  --text-2xl: 28px;             /* Section headers */
  --text-3xl: 36px;             /* Page headers */
  --text-4xl: 42px;             /* Large headers */
  --text-5xl: 56px;             /* Hero subtitles */
  --text-6xl: 72px;             /* Standard hero */
  --text-7xl: 96px;             /* Large hero */
  --text-8xl: 140px;            /* Ultra hero - BAKASANA title */

  /* Unified font weights */
  --font-light: 300;            /* Primary light weight */
  --font-normal: 400;           /* Standard weight */
  --font-medium: 500;           /* Medium weight for emphasis */

  /* Opacity levels for whisper effect */
  --opacity-whisper: 0.4;       /* Ultra-subtle elements */
  --opacity-subtle: 0.6;        /* Subtle elements */
  --opacity-soft: 0.7;          /* Soft hover states */
  --opacity-visible: 0.85;      /* Main hero title */

  /* Aliasy dla kompatybilności */
  --primary: var(--charcoal);
  --secondary: var(--stone);
  --background: var(--sanctuary);
  --accent: var(--temple-gold);
}

/* ===== BAKASANA RECTANGULAR DESIGN SYSTEM ===== */
/* Perfect zero-radius components for ultra-minimalist aesthetic */

.rectangular {
  border-radius: 0 !important;
  box-shadow: 0 1px 3px rgba(26, 24, 22, 0.04), 0 1px 2px rgba(26, 24, 22, 0.06);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.rectangular:hover {
  box-shadow: 0 4px 6px rgba(26, 24, 22, 0.07), 0 2px 4px rgba(26, 24, 22, 0.06);
  transform: translateY(-1px);
}

.rectangular-subtle {
  border-radius: 0 !important;
  box-shadow: 0 1px 2px rgba(26, 24, 22, 0.03);
}

.rectangular-elevated {
  border-radius: 0 !important;
  box-shadow: 0 10px 25px rgba(26, 24, 22, 0.08), 0 4px 6px rgba(26, 24, 22, 0.04);
}

/* Rectangular buttons with minimalist elegance */
.rectangular-button {
  border-radius: 0 !important;
  border: 1px solid var(--charcoal);
  background: transparent;
  padding: 0.75rem 2rem;
  font-family: var(--font-secondary);
  font-size: 0.75rem;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.rectangular-button:hover {
  opacity: 0.7;
  transform: translateY(-1px);
}

.rectangular-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--temple-gold);
}

/* Rectangular form controls */
.rectangular-input {
  border-radius: 0 !important;
  border: 1px solid var(--stone-light);
  background: var(--sanctuary);
  padding: 0.75rem 1rem;
  font-family: var(--font-secondary);
  font-size: 0.875rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.rectangular-input:focus {
  outline: none;
  border-color: var(--temple-gold);
  box-shadow: 0 0 0 2px rgba(184, 147, 92, 0.1);
}

.rectangular-input:error {
  border-color: var(--temple-gold);
}

/* Rectangular cards with perfect spacing */
.rectangular-card {
  border-radius: 0 !important;
  background: var(--sanctuary);
  border: 1px solid var(--stone-light);
  padding: 2rem;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.rectangular-card:hover {
  box-shadow: 0 4px 12px rgba(26, 24, 22, 0.08);
  transform: translateY(-2px);
}

/* ===== GLOBAL RULES FOR ULTRA-MINIMALISM ===== */
* {
  box-sizing: border-box;
  border-radius: 0 !important; /* Zero rounded corners globally - BAKASANA RULE */
}

/* ENFORCE ZERO BORDER-RADIUS ON ALL ELEMENTS */
*,
*::before,
*::after,
button,
input,
textarea,
select,
.btn,
.button,
.card,
.badge,
.rectangular,
.rectangular-subtle,
.elegant-border {
  border-radius: 0 !important;
}

html {
  scroll-behavior: auto; /* Changed to auto for better performance */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-variant-ligatures: common-ligatures;
  font-kerning: normal;
}

/* ===== ENTERPRISE HERO ENHANCEMENTS ===== */

/* Performance optimization for animated elements - removed will-change for better scroll */
.hero-element {
  contain: layout style paint;
}

/* Smooth cursor transitions */
* {
  cursor: default;
}

a, button, [role="button"], input[type="submit"] {
  cursor: pointer;
}

/* Focus states without outline but with opacity */
button:focus,
a:focus,
input:focus {
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

/* Enterprise-level selection styling */
::selection {
  background-color: var(--enterprise-brown);
  color: white;
}

::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}

/* Subtle animations for staggered loading */
@keyframes enterpriseFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enterprise-fade {
  animation: enterpriseFadeIn 1s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

/* Golden ratio spacing utilities */
.golden-spacing {
  margin-bottom: calc(1.618 * 1rem);
}

.golden-padding {
  padding: calc(1.618 * 1rem);
}

body {
  font-family: 'Lato', 'Source Sans Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--sanctuary);
  overflow-x: hidden;
}

/* ===== PRECISE TYPOGRAPHY HIERARCHY ===== */

/* LOGO */
.logo {
  font-family: 'Montserrat', sans-serif;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: var(--charcoal);
}

/* HERO TITLE - WARM MINIMAL WHISPER */
.hero-title {
  font-family: var(--font-primary);
  font-size: clamp(72px, 10vw, var(--text-8xl)); /* 140px on desktop */
  font-weight: var(--font-whisper);               /* Ultra-light 200 */
  letter-spacing: 0.22em;                         /* Slightly less spacing - more human */
  line-height: 1.05;                              /* Slightly looser for warmth */
  color: var(--charcoal);
  opacity: var(--opacity-visible);                /* 0.85 - barely visible effect */
  position: relative;
  top: -16px;                                     /* Slightly lower - more grounded */
  animation: fadeInUp 1.2s ease-out;
  text-shadow: 0 0 40px rgba(44, 41, 40, 0.03);  /* Warmer glow */
}

/* UNIFIED TYPOGRAPHY CLASSES */
.section-header {
  font-family: var(--font-primary);
  font-size: clamp(36px, 5vw, 56px);
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-wide);
  line-height: 1.3;
  color: var(--charcoal);
}

.card-title {
  font-family: var(--font-primary);
  font-size: 28px;
  font-weight: var(--font-normal);
  letter-spacing: var(--letter-spacing-wide);
  line-height: 1.4;
  color: var(--charcoal);
}

.body-text {
  font-family: var(--font-secondary);
  font-size: 15px;
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-normal);
  line-height: 1.8;
  color: var(--charcoal);
}

.nav-link {
  font-family: var(--font-secondary);
  font-size: 13px;
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-normal);
  text-transform: none;
  color: var(--charcoal);
}

.subtle-text {
  font-family: var(--font-secondary);
  font-size: 12px;
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
  color: var(--stone);
}

/* Standard HTML elements */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-wide);
  line-height: 1.3;
  color: var(--charcoal);
  margin-bottom: 1.5rem;
}

h1 { font-size: clamp(64px, 8vw, 128px); letter-spacing: var(--letter-spacing-widest); line-height: 1.1; }
h2 { font-size: clamp(36px, 5vw, 56px); letter-spacing: var(--letter-spacing-wider); }
h3 { font-size: 28px; letter-spacing: var(--letter-spacing-wide); }

p {
  font-family: var(--font-secondary);
  font-size: 15px;
  font-weight: var(--font-light);
  letter-spacing: var(--letter-spacing-normal);
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: var(--charcoal);
}

/* ===== PROFESSIONAL NAVBAR - OLD MONEY STYLE ===== */
/* Nowy navbar używa Tailwind CSS - wszystkie style są w komponencie React */

/* ===== SEKCJA ZAJĘCIA ONLINE - NOWA SEKCJA ===== */
.online-section {
  background: linear-gradient(135deg, var(--whisper) 0%, var(--sanctuary) 100%);
  padding: 100px 0;
  text-align: center;
}





.online-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 200;
  color: var(--charcoal);
  margin-bottom: 20px;
  letter-spacing: 0.05em;
}

.mobile-menu-button:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

.hamburger-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 18px;
  position: relative;
}

/* Enhanced hamburger lines */
.hamburger-line {
  width: 24px;
  height: 2px;
  background: var(--charcoal);
  border-radius: 1px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  position: absolute;
}

.hamburger-line:nth-child(1) {
  top: 0;
}

.hamburger-line:nth-child(2) {
  top: 8px;
}

.hamburger-line:nth-child(3) {
  top: 16px;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg);
  top: 8px;
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg);
  top: 8px;
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 40; /* Optimized: Proper stacking context */
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

/* Mobile menu - Enterprise */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: min(400px, 90vw);
  height: 100vh;
  background: white;
  z-index: 45; /* Optimized: Above overlay but below skip-link */
  display: flex;
  flex-direction: column;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
}

.mobile-menu.open {
  right: 0;
}

.mobile-menu-content {
  flex: 1;
  padding: 80px 30px 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Mobile navigation links */
.mobile-nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-nav-item {
  opacity: 0;
  transform: translateX(30px);
  animation: slideInMobile 0.4s ease-out forwards;
}

.mobile-nav-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-item:nth-child(2) { animation-delay: 0.15s; }
.mobile-nav-item:nth-child(3) { animation-delay: 0.2s; }
.mobile-nav-item:nth-child(4) { animation-delay: 0.25s; }
.mobile-nav-item:nth-child(5) { animation-delay: 0.3s; }
.mobile-nav-item:nth-child(6) { animation-delay: 0.35s; }

.mobile-nav-link {
  display: block;
  font-family: var(--font-primary);
  font-size: 18px;
  font-weight: 400;
  color: var(--charcoal);
  text-decoration: none;
  padding: 16px 20px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 1px solid transparent;
}

.mobile-nav-link:hover {
  background: var(--whisper);
  color: var(--temple-gold);
  transform: translateX(4px);
  border-color: rgba(212, 175, 55, 0.2);
}

.mobile-nav-link:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

.mobile-nav-link.active {
  background: rgba(212, 175, 55, 0.1);
  color: var(--temple-gold);
  font-weight: 500;
  border-color: rgba(212, 175, 55, 0.3);
}

.mobile-nav-link.online-classes {
  background: linear-gradient(135deg, var(--temple-gold), var(--golden-amber));
  color: white;
  font-weight: 500;
}

.mobile-nav-link.online-classes:hover {
  transform: translateX(4px) scale(1.02);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

/* Mobile dropdown */
.mobile-dropdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-dropdown-content {
  margin-left: 20px;
  padding-left: 20px;
  border-left: 2px solid var(--whisper);
}

.mobile-dropdown-section {
  margin-bottom: 12px;
}

.mobile-dropdown-header {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--stone);
  margin-bottom: 8px;
  padding-left: 20px;
}

.mobile-nav-link.sub-link {
  font-size: 16px;
  margin-left: 0;
  padding-left: 20px;
  color: var(--stone);
}

.mobile-nav-link.sub-link:hover {
  color: var(--temple-gold);
}

/* ===== ANIMATIONS - ENTERPRISE LEVEL ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInMobile {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== RESPONSIVE NAVIGATION ===== */

@media (max-width: 768px) {
  .navigation {
    padding: 16px 4%;
  }
  
  .navigation.scrolled {
    padding: 12px 4%;
  }
  
  .logo {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .mobile-menu {
    width: 100vw;
    right: -100vw;
  }
  
  .mobile-menu-content {
    padding: 100px 20px 40px;
  }
  
  .mobile-nav-link {
    font-size: 16px;
    padding: 14px 16px;
  }
}

.mobile-nav-link {
  letter-spacing: 0.1em;
  color: var(--charcoal);
  text-decoration: none;
  padding: 24px 0;
  transition: opacity 300ms ease;
  border-bottom: 1px solid transparent;
}

.mobile-nav-link.sub-link {
  font-size: 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  padding: 16px 0;
  opacity: 0.8;
  margin-left: 20px;
}

.mobile-nav-link.online-classes {
  color: var(--temple-gold);
  font-weight: 500;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
  opacity: 0.6;
}

.mobile-nav-link:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 4px;
}

/* Hide mobile menu on desktop */
@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }

  .mobile-menu {
    display: none;
  }
}

/* Hamburger menu dla mobile */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 0;
    flex-direction: column;
    background: var(--sanctuary);
    width: 100%;
    height: 100vh;
    justify-content: center;
    gap: 32px;
    transition: 0.3s;
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .nav-link {
    font-size: 24px;
    font-family: 'Cormorant Garamond', serif;
  }
  
  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    border: none;
    background: transparent;
    margin-top: 0;
  }
  
  .dropdown-item {
    padding: 8px 0;
    font-size: 18px;
    border-bottom: none;
  }
  
  .dropdown-header {
    padding: 16px 0 8px;
    font-size: 14px;
    background: transparent;
    color: var(--temple-gold);
  }
}

/* ===== SEKCJA ZAJĘCIA ONLINE - NOWA SEKCJA ===== */
.online-section {
  background: linear-gradient(135deg, var(--whisper) 0%, var(--sanctuary) 100%);
  padding: 100px 0;
  text-align: center;
}

.online-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 200;
  color: var(--charcoal);
  margin-bottom: 20px;
  letter-spacing: 0.05em;
}

.online-subtitle {
  font-size: 18px;
  color: var(--temple-gold);
  font-style: italic;
  margin-bottom: 60px;
  font-family: 'Inter', sans-serif;
}

.online-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 900px;
  margin: 0 auto;
}

.online-feature {
  text-align: center;
}

.feature-icon {
  font-size: 36px;
  color: var(--temple-gold);
  margin-bottom: 16px;
}

.feature-title {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--charcoal);
  font-family: 'Cormorant Garamond', serif;
  font-weight: 400;
}

.feature-description {
  font-size: 14px;
  color: var(--stone);
  line-height: 1.6;
  font-family: 'Inter', sans-serif;
}

@media (max-width: 768px) {
  .online-features {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .online-title {
    font-size: 36px;
  }
}

/* Links - Warm minimal hover effects */
a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  opacity: 0.75; /* Slightly more visible on hover */
  transform: translateY(-0.5px); /* Subtle lift - more human */
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  width: 100%;
  filter: brightness(0.96) contrast(1.04) saturate(1.02);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

img:hover {
  filter: brightness(1.02) contrast(1.08) saturate(1.05);
  transform: scale(1.02);
}

/* Specjalne stylowanie dla obrazów w kartach */
.destination-card img,
.retreat-card img,
.blog-card img {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.destination-card:hover img,
.retreat-card:hover img,
.blog-card:hover img {
  filter: brightness(1.04) contrast(1.12) saturate(1.08);
  transform: scale(1.05);
}

/* ===== NAVIGATION OPTIMIZATION ===== */
.navbar-optimized {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 80px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: background-color, backdrop-filter;
}

.navbar-scrolled {
  background: rgba(248, 246, 244, 0.9);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(139, 115, 85, 0.1);
  box-shadow: 0 2px 10px rgba(26, 24, 22, 0.04);
}

.navbar-transparent {
  background: transparent;
  backdrop-filter: blur(15px);
}

/* ===== MOBILE MENU OPTIMIZATION ===== */
.mobile-menu-optimized {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 90;
  background: rgba(248, 246, 244, 0.95);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(139, 115, 85, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform, max-height;
}

.mobile-menu-open {
  opacity: 1;
  transform: translateY(0);
  max-height: 100vh;
  visibility: visible;
}

.mobile-menu-closed {
  opacity: 0;
  transform: translateY(-8px);
  max-height: 0;
  visibility: hidden;
  overflow: hidden;
}

/* ===== HERO SECTION - OPTIMIZED FOR NAVBAR ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: linear-gradient(180deg,
    var(--dawn-cream) 0%,
    var(--dawn-peach) 40%,
    var(--warm-sand) 100%
  );
  position: relative;
  padding: 80px var(--element-breathing) 0;
  overflow: hidden;
  z-index: 1;
}

/* Hero breathing animation */
.hero-breathing {
  position: relative;
  z-index: 1;
}

/* Ensure hero content is properly positioned */
.hero-content-optimized {
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 0;
  text-align: center;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 1024px) {
  .navbar-optimized {
    padding: 0 1.5rem;
  }

  .hero {
    padding: 80px 6% 0;
  }

  .hero-content-optimized {
    padding: 1.5rem 0;
  }
}

@media (max-width: 768px) {
  .navbar-optimized {
    height: 70px;
  }

  .hero {
    padding: 70px 4% 0;
    min-height: calc(100vh - 70px);
  }

  .hero-content-optimized {
    padding: 1rem 0;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 70px 2% 0;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.navbar-optimized,
.hero-breathing,
.hero-content-optimized {
  will-change: transform, opacity;
  transform: translateZ(0); /* Hardware acceleration */
}

/* Smooth transitions for better UX */
.navbar-optimized * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .navbar-optimized,
  .hero-breathing,
  .hero-content-optimized,
  .mobile-menu-optimized {
    transition: none;
    animation: none;
  }

  .hero-content-optimized {
    transform: none;
  }
}

.hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(193, 155, 104, 0.03) 0%,
    transparent 50%
  );
  animation: gentleRotate 120s linear infinite;
  pointer-events: none;
}

@keyframes gentleRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hero-content {
  max-width: 1000px;
  padding: 0;
  position: relative;
  z-index: 10;
  animation: fadeInUp 1.5s ease-out;
  margin-top: 0; /* Remove any top margin conflicts */
}

/* Hero subtitle - whisper effect */
.hero-subtitle {
  font-family: var(--font-secondary);
  font-size: var(--text-base);                   /* 14px - slightly larger */
  font-weight: var(--font-light);                /* 300 weight */
  color: var(--stone);
  opacity: var(--opacity-subtle);                /* 0.6 opacity */
  margin: var(--micro-spacing) 0 0 0;            /* 32px top spacing */
  letter-spacing: 0.1em;
  text-transform: none;
}

/* Hero quote - poetic accent */
.hero-quote {
  font-family: var(--font-primary);
  font-size: 16px;
  font-weight: var(--font-light);               /* 300 weight */
  color: var(--stone);
  opacity: var(--opacity-soft);                 /* 0.7 opacity */
  font-style: italic;
  letter-spacing: 0.05em;
  margin: var(--micro-spacing) 0;               /* 32px spacing */
  animation: fadeInDelayed 1.8s ease-out forwards;
}

/* Hero meta - locations at bottom */
.hero-meta {
  font-family: var(--font-secondary);
  font-size: 11px;                               /* Even smaller */
  font-weight: var(--font-whisper);              /* 200 weight */
  color: var(--stone);
  opacity: var(--opacity-whisper);               /* 0.4 - barely visible */
  letter-spacing: 0.3em;
  text-transform: none;
  margin: var(--micro-spacing) 0 0 0;            /* 32px top spacing */
  animation: fadeInDelayed 2s ease-out forwards;
  opacity: 0;                                     /* Start invisible */
}

/* Hero CTA - Ultra-minimal call to action */
.hero-cta {
  margin: var(--hero-spacing) 0 0 0;             /* 120px top spacing */
  animation: fadeInDelayed 2.5s ease-out forwards;
  opacity: 0;                                     /* Start invisible */
}

.hero-cta-link {
  font-family: var(--font-secondary);
  font-size: var(--text-sm);                     /* 12px */
  font-weight: var(--font-light);                /* 300 weight */
  color: var(--temple-gold);
  letter-spacing: 0.2em;
  text-transform: none;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 300ms ease;
  opacity: 0.7;
}

.hero-cta-link:hover {
  opacity: 1; /* BAKASANA RULE: Only opacity changes on hover */
}

/* Hero quote - osobisty akcent */
.hero-quote {
  font-family: 'Cormorant Garamond', serif;
  font-size: 18px;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  letter-spacing: 0.05em;
}

/* ===== ENHANCED ANIMATIONS FOR ENTERPRISE FEEL ===== */

/* Fade-in animation for locations - more organic */
@keyframes fadeInDelayed {
  0% { 
    opacity: 0; 
    transform: translateX(-50%) translateY(12px);
    filter: blur(2px);
  }
  100% { 
    opacity: var(--opacity-whisper); 
    transform: translateX(-50%) translateY(0);
    filter: blur(0);
  }
}

/* Enhanced fade-in for main elements */
@keyframes fadeInUp {
  0% { 
    opacity: 0; 
    transform: translateY(40px);
    filter: blur(1px);
  }
  100% { 
    opacity: 1; 
    transform: translateY(0);
    filter: blur(0);
  }
}

/* Subtle breathing animation for spiritual elements */
@keyframes breathe {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.02);
  }
}

/* Warm glow animation for interactive elements */
@keyframes warmGlow {
  0% { 
    box-shadow: 0 0 0 rgba(193, 155, 104, 0);
  }
  100% { 
    box-shadow: 0 0 20px rgba(193, 155, 104, 0.1);
  }
}

/* Elegant slide-in for cards */
@keyframes slideInRight {
  0% { 
    opacity: 0;
    transform: translateX(60px);
  }
  100% { 
    opacity: 1;
    transform: translateX(0);
  }
}

/* Smooth scale for hover effects */
@keyframes smoothScale {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

/* Gentle pulse for spiritual elements */
@keyframes gentlePulse {
  0%, 100% { 
    opacity: 0.6; 
    transform: translate(-50%, -50%) scale(1);
  }
  50% { 
    opacity: 1; 
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* Organic float for floating elements */
@keyframes organicFloat {
  0%, 100% { 
    transform: translateY(0px);
  }
  50% { 
    transform: translateY(-6px);
  }
}

.subtle-link {
  font-size: 12px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border-bottom: 1px solid transparent;
  transition: all 300ms ease;
}

.subtle-link:hover {
  opacity: 0.7; /* BAKASANA RULE: Only opacity changes on hover */
}

/* ===== LAYOUT SYSTEM - Przestrzeń jako Luksus ===== */
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 120px 8%; /* BAKASANA STANDARD: 120px vertical, 8% horizontal */
}

.section {
  padding: var(--section-padding); /* BAKASANA STANDARD: 120px between sections */
  background: transparent; /* Zero-container philosophy */
  position: relative;
  overflow: hidden;
}

.section-breathe {
  padding: 180px 15%; /* Hojne odstępy dla luksusowego odczucia */
}

/* ===== PŁYNNE PRZEJŚCIA MIĘDZY SEKCJAMI ===== */
.section-smooth {
  position: relative;
  z-index: 1;
}

.section-smooth::before {
  content: '';
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  pointer-events: none;
  z-index: -1;
}

.section-smooth::after {
  content: '';
  position: absolute;
  bottom: -60px;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  pointer-events: none;
  z-index: -1;
}

/* Warianty dla różnych sekcji */
.section-warm {
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    var(--dawn-peach) 30%,
    var(--warm-sand) 100%
  );
}

.section-spiritual {
  background: linear-gradient(
    180deg,
    var(--rice) 0%,
    var(--whisper) 50%,
    var(--sanctuary) 100%
  );
}

.section-testimonials {
  background: linear-gradient(
    135deg,
    var(--warm-black) 0%,
    var(--warm-black-gradient) 100%
  );
  color: var(--sanctuary);
}

/* ===== DESTINATION CARDS - POETYCKIE KARTY ===== */
.destinations {
  padding: var(--section-padding);
  background: var(--sanctuary);
  position: relative;
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
  gap: 60px;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
}

.destination-card {
  background: rgba(255, 255, 255, 0.6);
  border: none;
  border-radius: 0;
  box-shadow: 
    0 1px 3px rgba(44, 41, 40, 0.02),
    0 4px 12px rgba(44, 41, 40, 0.04);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(10px);
}

.destination-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(253, 249, 243, 0.8) 0%,
    rgba(249, 246, 242, 0.9) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08);
}

.destination-card:hover::before {
  opacity: 1;
}

.card-image {
  aspect-ratio: 4/3;
  background-size: cover;
  background-position: center;
  filter: brightness(0.95) contrast(1.05); /* zamiast grayscale */
  transition: all 600ms ease;
  position: relative;
}

/* Hover effects for destination images */
.destination-image {
  filter: brightness(0.95) contrast(1.05); /* zamiast grayscale */
}

.destination-image:hover {
  filter: brightness(1) contrast(1.1);
}

/* Hidden overlay with details */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 400ms ease;
  padding: 32px;
  text-align: center;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.1em;
}

.destination-card:hover .card-overlay {
  opacity: 1;
}

.card-content {
  padding: 48px 32px;
  text-align: center;
}

.card-meta {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 16px;
  opacity: 0;                                           /* Hidden initially */
  transition: opacity 300ms ease;
}

.destination-card:hover .card-meta {
  opacity: 1;                                           /* Show on hover */
}

.card-description {
  margin: 24px 0 32px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0;                                           /* Hidden initially */
  transition: opacity 300ms ease;
}

.destination-card:hover .card-description {
  opacity: 0.8;                                         /* Show on hover */
}

.card-details {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: var(--opacity-whisper);                      /* Ultra-subtle */
  transition: opacity 300ms ease;
  position: absolute;
  bottom: 16px;
  right: 16px;
}

.card-details:hover {
  opacity: var(--opacity-soft);
}

/* ===== GHOST BUTTONS - DUCHOWA PROSTOTA Z CIEPŁEM I LUDZKOŚCIĄ ===== */
.btn-ghost {
  padding: 18px 54px;
  border: 1px solid var(--stone);
  background: transparent;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-ghost::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(193, 155, 104, 0.1),
    transparent
  );
  transition: left 0.8s ease;
}

.btn-ghost:hover::before {
  left: 100%;
}

.btn-ghost:hover {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(193, 155, 104, 0.1),
    0 8px 24px rgba(193, 155, 104, 0.05);
}

/* Enhanced breathing animation for buttons */
@keyframes breathe {
  0%, 100% { 
    transform: translateY(-1px) scale(1);
    box-shadow: 0 2px 8px rgba(193, 155, 104, 0.05);
  }
  50% { 
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 4px 12px rgba(193, 155, 104, 0.1);
  }
}

/* Primary button variant */
.btn-primary {
  color: var(--temple-gold);
  border-color: var(--temple-gold);
  background: rgba(193, 155, 104, 0.05);
}

.btn-primary:hover {
  background: rgba(193, 155, 104, 0.1);
  transform: translateY(-2px);
  box-shadow: 
    0 6px 16px rgba(193, 155, 104, 0.15),
    0 12px 32px rgba(193, 155, 104, 0.08);
}

/* Accent button variant */
.btn-accent {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
  background: rgba(193, 155, 104, 0.03);
}

.btn-accent:hover {
  background: rgba(193, 155, 104, 0.08);
  transform: translateY(-2px);
}

/* ===== SECTION DIVIDERS - SACRED GEOMETRY ===== */
.section-divider {
  width: 120px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(193, 155, 104, 0.3) 15%,
    rgba(193, 155, 104, 0.6) 35%,
    rgba(193, 155, 104, 0.8) 50%,
    rgba(193, 155, 104, 0.6) 65%,
    rgba(193, 155, 104, 0.3) 85%,
    transparent 100%
  );
  margin: 80px auto;
  position: relative;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.section-divider:hover {
  opacity: 1;
}

/* Bardzo subtelny element w centrum */
.section-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: var(--temple-gold);
  opacity: 0.8;
  transform: translate(-50%, -50%);
  animation: gentlePulse 3s ease-in-out infinite;
}

@keyframes gentlePulse {
  0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.btn-accent {
  border-color: var(--temple-gold);
  color: var(--temple-gold);
}

/* ===== ENHANCED ABOUT JULIA SECTION - ENTERPRISE AUTHENTICITY ===== */
.about-julia-section {
  background: linear-gradient(180deg, var(--dawn-peach) 0%, var(--sanctuary) 100%);
  position: relative;
  overflow: hidden;
  padding: 140px 0;
}

/* Subtelny wzór w tle */
.about-julia-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(201, 165, 117, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

/* Enhanced Divider */
.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 80px 0;
  position: relative;
}

.divider-line {
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--temple-gold), transparent);
}

.divider-symbol {
  margin: 0 30px;
  font-size: 24px;
  color: var(--temple-gold);
  opacity: 0.6;
}

/* Asymetryczny layout */
.about-julia-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 100px;
  align-items: center;
  position: relative;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 8%;
}

/* Zdjęcie wystaje poza sekcję */
.julia-image-wrapper {
  position: relative;
  margin-top: -60px;
  margin-bottom: -60px;
}

.julia-image {
  filter: contrast(1.1) brightness(0.98);
  box-shadow: 0 40px 80px rgba(0,0,0,0.1);
  transition: all 500ms ease;
}

.julia-image:hover {
  filter: contrast(1.15) brightness(1);
  box-shadow: 0 50px 100px rgba(0,0,0,0.15);
}

/* Typografia dopasowana do hero */
.about-julia-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 72px;
  font-weight: 300;
  letter-spacing: 0.1em;
  color: var(--charcoal);
  margin-bottom: 40px;
  line-height: 1.2;
}

/* Złota linia pod tytułem */
.title-accent {
  width: 60px;
  height: 2px;
  background: var(--temple-gold);
  margin: 20px 0 40px 0;
}

/* Cytat w złotej ramce */
.julia-quote {
  border-left: 3px solid var(--temple-gold);
  padding-left: 30px;
  margin: 40px 0;
  font-size: 22px;
  line-height: 1.8;
  color: var(--temple-gold);
  font-style: normal;
  font-family: 'Caveat', cursive;
}

/* Statystyki w eleganckich boxach */
.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 60px;
}

.stat-box {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255,255,255,0.5);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border-radius: 0; /* BAKASANA RULE: Zero border-radius */
}

.stat-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
}

/* Responsive dla sekcji About Julia */
@media (max-width: 1024px) {
  .about-julia-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .julia-image-wrapper {
    margin-top: 0;
    margin-bottom: 0;
  }
  
  .about-julia-title {
    font-size: 48px;
  }
  
  .stats-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .about-julia-title {
    font-size: 36px;
  }
  
  .julia-quote {
    font-size: 18px;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* ===== UTILITY CLASSES FOR FONT FAMILIES ===== */
.font-primary {
  font-family: var(--font-primary);
}

.font-secondary {
  font-family: var(--font-secondary);
}

.font-accent {
  font-family: var(--font-accent);
}

/* ===== FOOTER - MINIMALIZM ===== */
.footer {
  background: var(--whisper);
  padding: 80px 8% 40px 8%;
  text-align: center;
}

.footer-content {
  max-width: 600px;
  margin: 0 auto;
}

.spiritual-greeting {
  font-family: 'Cormorant Garamond', serif;
  font-size: 18px;
  font-weight: 300;
  color: var(--temple-gold);
  margin-bottom: 48px;
  letter-spacing: 1px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 32px;
}

.footer-link {
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  text-decoration: none;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: opacity 300ms ease;
}

.footer-link:hover {
  opacity: 0.6;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 32px;
}

.social-icon {
  width: 20px;
  height: 20px;
  opacity: 0.4;
  transition: opacity 300ms ease;
}

.social-icon:hover {
  opacity: 0.8;
}

.copyright {
  font-size: 10px;
  font-weight: 300;
  color: var(--stone);
  opacity: 0.5;
  letter-spacing: 0.3px;
}



/* ===== TESTIMONIALS - CIEPLEJSZE ===== */
.testimonial-card {
  background: rgba(255, 255, 255, 0.05); /* bardzo subtelne dla ciemnego tła */
  border-left: 2px solid var(--temple-gold); /* złoty akcent z boku */
  padding: 24px;
  border-radius: 0; /* zachowaj sharp edges */
}

/* ===== JULIA SIGNATURE - OSOBISTY DOTYK ===== */
.julia-signature {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 40px 0;
}

.signature-line {
  height: 1px;
  width: 40px;
  background: var(--temple-gold);
  opacity: 0.5;
}

.signature-text {
  font-family: 'Cormorant Garamond', serif;
  font-size: 32px;
  font-weight: 300;
  font-style: italic;
  color: var(--temple-gold);
}

/* ===== ENHANCED TESTIMONIALS SECTION ===== */
.testimonials-section {
  background: var(--warm-black);
  padding: 120px 0;
  position: relative;
}

.testimonials-section .section-divider {
  margin: 80px 0;
}

.testimonials-section .divider-line {
  background: linear-gradient(90deg, transparent, var(--temple-gold), transparent);
}

.testimonials-section .divider-symbol {
  color: var(--temple-gold);
}

/* ===== ENHANCED CONTACT SECTION ===== */
.contact-section {
  background: linear-gradient(180deg, #FAF5F0 0%, #FDF9F3 100%);
  padding: 120px 0;
  position: relative;
}

.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin: 60px auto;
  max-width: 1000px;
  padding: 0 8%;
}

.contact-card {
  text-align: center;
  padding: 40px 30px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(201, 165, 117, 0.1);
  text-decoration: none;
  display: block;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  border-color: rgba(var(--temple-gold-rgb), 0.3);
}

/* ===== ENHANCED BLOG SECTION ===== */
.blog-section {
  background: linear-gradient(180deg, #FDF9F3 0%, #FAF5F0 100%);
  padding: 120px 0;
  position: relative;
}

.blog-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(201, 165, 117, 0.02) 0%, transparent 70%);
  pointer-events: none;
}

.blog-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin: 60px auto;
  max-width: 1200px;
  padding: 0 8%;
}

.blog-card {
  background: rgba(255, 255, 255, 0.8);
  padding: 30px;
  transition: all 0.3s ease;
  border: 1px solid rgba(201, 165, 117, 0.1);
}

.blog-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  .contact-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .blog-cards-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .contact-card {
    padding: 30px 20px;
  }
}

/* ===== ONLINE CTA PULSOWANIE ===== */
@keyframes subtle-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.85; }
}

.online-cta {
  animation: subtle-pulse 3s ease-in-out infinite;
}

/* ===== ONLINE CLASSES CARD ===== */
.online-classes-card {
  background: linear-gradient(135deg, var(--pure-white) 0%, #FAF8F4 100%);
  border: 1px solid var(--stone-light);
}

/* ===== BAKASANA UTILITY CLASSES ===== */

/* Typography utilities */
.font-montserrat { font-family: var(--font-primary); }
.font-lato { font-family: var(--font-secondary); }
.font-caveat { font-family: var(--font-accent); }
.letter-spacing-wide { letter-spacing: 0.1em; }
.letter-spacing-wider { letter-spacing: 0.2em; }

/* Color utilities */
.text-sanctuary { color: var(--sanctuary); }
.text-charcoal { color: var(--charcoal); }
.text-stone { color: var(--stone); }
.text-temple-gold { color: var(--temple-gold); }
.text-sage-green { color: var(--sage-green); }
.bg-sanctuary { background-color: var(--sanctuary); }
.bg-charcoal { background-color: var(--charcoal); }
.bg-whisper { background-color: var(--whisper); }
.bg-rice { background-color: var(--rice); }

/* Extended warm & friendly color utilities */
.text-cream { color: var(--cream); }
.text-warm-peach { color: var(--warm-peach); }
.text-soft-sage { color: var(--soft-sage); }
.text-terracotta { color: var(--terracotta); }
.text-blush { color: var(--blush); }
.text-friendly-coral { color: var(--friendly-coral); }
.text-calm-lavender { color: var(--calm-lavender); }

.bg-cream { background-color: var(--cream); }
.bg-warm-peach { background-color: var(--warm-peach); }
.bg-soft-sage { background-color: var(--soft-sage); }
.bg-terracotta { background-color: var(--terracotta); }
.bg-blush { background-color: var(--blush); }
.bg-friendly-coral { background-color: var(--friendly-coral); }
.bg-calm-lavender { background-color: var(--calm-lavender); }

/* Spacing utilities */
.section-padding { padding: var(--section-padding); }
.container-padding { padding: 0 var(--element-breathing); }
.breathe-spacing { padding: var(--breathe-spacing) 0; }

/* Layout utilities */
.text-center { text-align: center; }
.max-width-content { max-width: var(--container-max); margin: 0 auto; }

/* Opacity utilities for whisper effects */
.opacity-whisper { opacity: var(--opacity-whisper); }
.opacity-subtle { opacity: var(--opacity-subtle); }
.opacity-soft { opacity: var(--opacity-soft); }
.opacity-visible { opacity: var(--opacity-visible); }

/* Unified hover utilities */
.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
  transition: transform var(--duration-normal) var(--ease-smooth);
}
.hover-lift-card:hover {
  transform: translateY(-4px) scale(1.02);
  transition: transform var(--duration-normal) var(--ease-smooth);
}
.hover-scale:hover {
  transform: scale(1.02);
  transition: transform var(--duration-normal) var(--ease-smooth);
}

/* Performance optimized animations */
.animate-optimized {
  will-change: transform, opacity;
  contain: layout style paint;
  transition: all var(--duration-normal) var(--ease-smooth);
}

/* ===== UNIFIED INTERACTION CLASSES ===== */

/* Standard hover effects - use these for consistency */
.hover-lift {
  transition: transform var(--duration-fast) var(--ease-smooth);
}
.hover-lift:hover {
  transform: var(--hover-lift);
}

.hover-scale {
  transition: transform var(--duration-fast) var(--ease-smooth);
}
.hover-scale:hover {
  transform: var(--hover-scale);
}

.hover-fade {
  transition: opacity var(--duration-fast) var(--ease-smooth);
}
.hover-fade:hover {
  opacity: var(--hover-opacity);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-smooth);
}
.hover-glow:hover {
  box-shadow: 0 8px 32px rgba(139, 115, 85, 0.15);
}

/* Focus states for accessibility */
.focus-ring {
  transition: box-shadow var(--duration-fast) var(--ease-smooth);
}
.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--enterprise-brown), 0 0 0 4px rgba(139, 115, 85, 0.2);
}

/* Loading states */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-shimmer {
  background: linear-gradient(90deg,
    var(--sanctuary) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    var(--sanctuary) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== ENTERPRISE RESPONSIVE SYSTEM - COMPLETE COVERAGE ===== */

/* Mobile First - Base styles above, overrides below */

/* Small Mobile (320px-479px) */
@media (max-width: 479px) {
  .hero-title { font-size: 36px; letter-spacing: 0.15em; }
  .section-header { font-size: 24px; }
  .card-title { font-size: 20px; }
  .container { padding: 60px 4%; }
  .section { padding: 60px 0; }
  .nav-links { gap: 16px; }
  .navigation { padding: 16px 4%; }
  .navigation.scrolled { padding: 12px 4%; }
}

/* Mobile (480px-767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .hero-title { font-size: 48px; letter-spacing: 0.2em; }
  .section-header { font-size: 32px; }
  .card-title { font-size: 24px; }
  .container { padding: 80px 5%; }
  .section { padding: 80px 0; }
  .nav-links { gap: 20px; }
  .destinations-grid {
    grid-template-columns: 1fr;
    gap: 48px;
  }
  .about-container {
    grid-template-columns: 1fr;
    gap: 48px;
  }
}

/* Tablet (768px-1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .hero-title { font-size: 72px; letter-spacing: 0.22em; }
  .section-header { font-size: 42px; }
  .card-title { font-size: 26px; }
  .container { padding: 100px 6%; }
  .section { padding: 100px 0; }
  .nav-links { gap: 28px; }
  .destinations-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;
  }
  .about-container {
    grid-template-columns: 1fr 1fr;
    gap: 60px;
  }
}

/* Desktop (1024px-1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
  .hero-title { font-size: clamp(96px, 8vw, 120px); }
  .section-header { font-size: 48px; }
  .container { padding: 120px 7%; }
  .section { padding: 120px 0; }
  .nav-links { gap: 36px; }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .hero-title { font-size: clamp(120px, 10vw, 140px); }
  .section-header { font-size: 56px; }
  .container { padding: 120px 8%; }
  .section { padding: 120px 0; }
  .nav-links { gap: 48px; }
}

/* ===== KLUCZOWE ZASADY IMPLEMENTACJI ===== */

/* 1. Zero Rounded Corners */
* { border-radius: 0 !important; }

/* 2. Tylko Opacity Hovers */
.hover-element:hover {
  opacity: 0.7;
  /* NIGDY: transform, color, background, shadow */
}

/* 3. Performance Optimizations */
.smooth-transition {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.gpu-acceleration {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 4. Enterprise Accessibility */
.focus-visible,
*:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  border-radius: 0;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  font-size: 14px;
  font-weight: 300;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-ghost {
    border-width: 2px;
  }

  .nav-link {
    text-decoration: underline;
  }

  .card-image {
    filter: contrast(1.2);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Force native scrolling for performance */
html {
  scroll-behavior: auto !important;
}

/* Disable mandala animations for better performance */
@media (prefers-reduced-motion: reduce) {
  .mandala-outer,
  .mandala-middle,
  .mandala-inner {
    animation: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --sanctuary: var(--soft-black);
    --charcoal: #e0e0e0;
    --stone: var(--stone);
    --whisper: var(--charcoal);
  }
}

/* MINIMAL SCROLLBAR */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--stone);
  opacity: 0.3;
}

/* ELEGANT NAVIGATION ANIMATIONS */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* GLASS MORPHISM NAVIGATION EFFECTS */
.glass-nav {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.85);
  border-bottom: 1px solid rgba(139, 115, 85, 0.08);
  box-shadow: 0 1px 20px rgba(139, 115, 85, 0.06);
}

.glass-nav-transparent {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: transparent;
}

/* LOGO GRADIENT GLOW */
.logo-gradient {
  background: linear-gradient(135deg, var(--temple-gold) 0%, var(--golden-amber) 50%, var(--temple-gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 8px rgba(184, 147, 92, 0.3));
  transition: filter 0.3s ease;
}

.logo-gradient:hover {
  filter: drop-shadow(0 0 12px rgba(184, 147, 92, 0.5));
}

/* =============================================
   SPECTACULAR HERO ANIMATIONS
   ============================================= */

/* Animated Sky Gradient */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Floating Clouds */
.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  opacity: 0.6;
}

.cloud::before,
.cloud::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
}

.cloud-1 {
  width: 100px;
  height: 40px;
  top: 20%;
  left: 10%;
  animation: float1 60s ease-in-out infinite;
}

.cloud-1::before {
  width: 50px;
  height: 50px;
  top: -25px;
  left: 10px;
}

.cloud-1::after {
  width: 60px;
  height: 40px;
  top: -15px;
  right: 10px;
}

.cloud-2 {
  width: 80px;
  height: 30px;
  top: 30%;
  right: 20%;
  animation: float2 80s ease-in-out infinite reverse;
}

.cloud-2::before {
  width: 40px;
  height: 40px;
  top: -20px;
  left: 15px;
}

.cloud-2::after {
  width: 50px;
  height: 30px;
  top: -10px;
  right: 15px;
}

.cloud-3 {
  width: 120px;
  height: 50px;
  top: 15%;
  right: 10%;
  animation: float3 100s ease-in-out infinite;
}

.cloud-3::before {
  width: 60px;
  height: 60px;
  top: -30px;
  left: 20px;
}

.cloud-3::after {
  width: 70px;
  height: 50px;
  top: -20px;
  right: 20px;
}

@keyframes float1 {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(20px) translateY(-10px); }
  50% { transform: translateX(-10px) translateY(5px); }
  75% { transform: translateX(15px) translateY(-5px); }
}

@keyframes float2 {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(-25px) translateY(10px); }
  66% { transform: translateX(15px) translateY(-8px); }
}

@keyframes float3 {
  0%, 100% { transform: translateX(0) translateY(0); }
  20% { transform: translateX(-15px) translateY(8px); }
  40% { transform: translateX(10px) translateY(-12px); }
  60% { transform: translateX(-8px) translateY(6px); }
  80% { transform: translateX(12px) translateY(-4px); }
}

/* Water Waves */
.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(180deg, transparent 0%, rgba(72, 187, 120, 0.1) 100%);
  border-radius: 1000px 1000px 0 0;
}

.wave-1 {
  animation: wave1 8s ease-in-out infinite;
  z-index: 3;
}

.wave-2 {
  animation: wave2 6s ease-in-out infinite reverse;
  z-index: 2;
  opacity: 0.7;
  height: 80px;
}

.wave-3 {
  animation: wave3 10s ease-in-out infinite;
  z-index: 1;
  opacity: 0.5;
  height: 60px;
}

@keyframes wave1 {
  0%, 100% { transform: translateX(0) scaleY(1); }
  50% { transform: translateX(-20px) scaleY(1.1); }
}

@keyframes wave2 {
  0%, 100% { transform: translateX(0) scaleY(1); }
  50% { transform: translateX(15px) scaleY(0.9); }
}

@keyframes wave3 {
  0%, 100% { transform: translateX(0) scaleY(1); }
  50% { transform: translateX(-10px) scaleY(1.05); }
}

/* Additional Hero Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(5deg);
  }
  75% {
    transform: translateY(-5px) rotate(-5deg);
  }
}

/* ===== SPIRITUAL ELEMENTS ===== */

/* Om Symbol Styling */
.om-symbol {
  font-family: 'Noto Sans Devanagari', serif;
  color: var(--om-symbol);
  font-size: 1.5rem;
  opacity: 0.8;
  display: inline-block;
}

/* Balinese Greeting */
.balinese-greeting {
  font-style: italic;
  color: var(--temple-gold);
  font-size: 0.9rem;
  letter-spacing: 0.05em;
  font-family: 'Lato', sans-serif;
  font-weight: 300;
}

/* Cultural Text Accents */
.temple-gold-text {
  color: var(--temple-gold);
  font-weight: 400;
}

.sage-text {
  color: var(--sage-green);
  font-weight: 400;
}

.sri-lankan-accent {
  color: var(--ocean-blue);
  font-weight: 400;
}

/* ===== LOTUS DECORATIVE ELEMENTS ===== */
.lotus-divider {
  text-align: center;
  margin: 60px 0;
  color: var(--sage-green);
  font-size: 1.2rem;
  opacity: 0.6;
}

.lotus-divider::before,
.lotus-divider::after {
  content: '❀';
  margin: 0 20px;
  opacity: 0.4;
}

/* ===== RETREAT CARD STYLING - ULTRA-MINIMAL ===== */
.retreat-card {
  background: transparent;
  border: none;
  margin-bottom: 80px;
  transition: opacity 0.2s ease;
}

.retreat-card:hover {
  opacity: 0.9;
}

.retreat-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 0; /* Sharp edges */
  display: block;
}

.retreat-image-container {
  margin-bottom: 30px;
  overflow: hidden;
}

/* ===== ENTERPRISE ENHANCEMENTS ===== */

/* Sacred Quote Styling */
.sacred-quote {
  font-family: 'Cormorant Garamond', serif;
  font-style: italic;
  font-weight: 300;
  letter-spacing: 0.02em;
  line-height: 1.6;
  color: var(--stone);
}

/* Enterprise Image Treatments */
.enterprise-image {
  position: relative;
  overflow: hidden;
}

.enterprise-image img {
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.enterprise-image:hover img {
  opacity: 0.9; /* BAKASANA RULE: Only opacity changes on hover */
}

/* Sacred Dividers */
.sacred-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 4rem 0;
}

.sacred-divider::before,
.sacred-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--stone);
  opacity: 0.3;
}

.sacred-divider-content {
  margin: 0 2rem;
  color: var(--stone);
  opacity: 0.6;
  font-size: 1.5rem;
}

/* ===== ABOUT PAGE - OLD MONEY ELEGANCE ===== */

.about-page-elegant {
  background: linear-gradient(135deg, 
    #fdfcf8 0%,
    #f9f7f2 30%,
    #f5f3ef 70%,
    #f1efeb 100%
  );
  min-height: 100vh;
  position: relative;
}

.about-page-elegant::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(193, 155, 104, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(193, 155, 104, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Hero Section - Old Money Refined (Zmniejszona wysokość o 20%) */
.about-hero-refined {
  padding: 80px 0 60px 0;
  background: linear-gradient(135deg, 
    rgba(253, 252, 248, 0.97) 0%,
    rgba(249, 247, 242, 0.99) 50%,
    rgba(245, 243, 239, 0.95) 100%
  );
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(193, 155, 104, 0.1);
}

.about-hero-refined::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23c19b68' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
}

.about-hero-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  position: relative;
  z-index: 10;
}

.about-hero-ornament {
  width: 80px;
  height: 1px;
  background: var(--temple-gold);
  margin: 0 auto 40px auto;
  opacity: 0.6;
  position: relative;
}

.about-hero-ornament::before,
.about-hero-ornament::after {
  content: '';
  position: absolute;
  top: -2px;
  width: 6px;
  height: 6px;
  background: var(--temple-gold);
  border-radius: 50%;
}

.about-hero-ornament::before {
  left: -10px;
}

.about-hero-ornament::after {
  right: -10px;
}

.about-hero-content-elegant {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.about-hero-name {
  font-family: var(--font-primary);
  font-size: 4.5rem;
  font-weight: 300;
  color: var(--charcoal);
  letter-spacing: 0.02em;
  margin: 0 0 20px 0;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 1s ease-out;
  transform: translateY(0);
}

.about-hero-divider {
  width: 120px;
  height: 1px;
  background: var(--temple-gold);
  margin: 0 auto 20px auto;
  opacity: 0.8;
}

.about-hero-credentials {
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  color: var(--stone);
  letter-spacing: 0.05em;
  margin: 0 0 20px 0;
  line-height: 1.4;
  text-transform: uppercase;
  font-weight: 400;
}

.about-hero-quote {
  font-family: var(--font-primary);
  font-size: 1.6rem;
  font-style: italic;
  color: var(--temple-gold);
  margin: 0;
  line-height: 1.4;
  position: relative;
  padding: 0 40px;
  animation: fadeInUp 1.2s ease-out;
  transform: translateY(0);
  opacity: 0.9;
}

.about-hero-quote::before,
.about-hero-quote::after {
  content: '"';
  position: absolute;
  font-size: 3rem;
  color: var(--temple-gold);
  opacity: 0.3;
  top: -10px;
}

.about-hero-quote::before {
  left: 0;
}

.about-hero-quote::after {
  right: 0;
}

/* Main Content - Refined Layout */
.about-content-refined {
  padding: 100px 0;
  background: #fdfcf8;
}

.about-content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-content-grid {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 6rem;
  align-items: start;
}

.about-photo-column {
  display: flex;
  justify-content: center;
  position: sticky;
  top: 100px;
}

.about-photo-frame {
  position: relative;
  padding: 20px;
  background: white;
  border-radius: 2px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 8px 40px rgba(0, 0, 0, 0.04);
  transform: rotate(-1deg);
  transition: all 0.3s ease;
}

.about-photo-frame:hover {
  transform: rotate(0deg) scale(1.02);
}

.about-photo-placeholder {
  width: 350px;
  height: 440px;
  background: linear-gradient(135deg, 
    #f9f7f2 0%,
    #f5f3ef 100%
  );
  border: 1px solid rgba(193, 155, 104, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.about-photo-ornament {
  width: 60px;
  height: 20px;
  background: var(--temple-gold);
  opacity: 0.1;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.about-photo-ornament-top {
  top: 20px;
  clip-path: polygon(0 0, 100% 0, 90% 100%, 10% 100%);
}

.about-photo-ornament-bottom {
  bottom: 20px;
  clip-path: polygon(10% 0, 90% 0, 100% 100%, 0 100%);
}

.about-photo-content {
  text-align: center;
  color: var(--temple-gold);
  z-index: 2;
}

.about-photo-text {
  margin-top: 20px;
  text-align: center;
}

.about-photo-label {
  display: block;
  font-size: 0.9rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 8px;
}

.about-photo-name {
  display: block;
  font-size: 1.2rem;
  color: var(--charcoal);
  font-family: var(--font-primary);
  font-style: italic;
}

.about-text-column {
  padding-top: 40px;
}

.about-text-content {
  max-width: 600px;
}

.about-text-intro {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-weight: 300;
}

.about-text-paragraph {
  margin: 0 0 32px 0;
  text-align: justify;
  hyphens: auto;
}

.about-text-paragraph:first-child {
  font-size: 1.3rem;
  color: var(--temple-gold);
  font-weight: 400;
}

/* Credentials Section - Refined */
.about-credentials-refined {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    var(--whisper) 0%,
    rgba(248, 246, 240, 0.95) 100%
  );
}

.about-credentials-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-credentials-header {
  text-align: center;
  margin-bottom: 60px;
}

.about-credentials-title {
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.about-credentials-subtitle {
  font-size: 1.1rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.about-credentials-grid-refined {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.about-credentials-grid-refined .about-credential-card:first-child {
  /* Usuń specjalne pozycjonowanie pierwszej karty */
}

.about-credential-card {
  background: white;
  padding: 3rem 2rem;
  border-radius: 8px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 8px 40px rgba(0, 0, 0, 0.04);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  transform: translateY(0);
}

.about-credential-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 20px 60px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.about-credential-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--temple-gold), rgba(193, 155, 104, 0.5));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.about-credential-card:hover::before {
  opacity: 1;
}

.about-credential-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.about-credential-icon-refined {
  font-size: 2.8rem;
  color: var(--temple-gold);
  margin-bottom: 24px;
  display: block;
  transition: transform 0.3s ease;
  opacity: 0.9;
}

.about-credential-card:hover .about-credential-icon-refined {
  transform: scale(1.1);
  opacity: 1;
}

.about-credential-content {
  text-align: center;
}

.about-credential-label-refined {
  font-size: 0.9rem;
  color: var(--stone);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 8px;
  font-family: var(--font-secondary);
}

.about-credential-value-refined {
  font-size: 1.8rem;
  color: var(--charcoal);
  font-weight: 300;
  font-family: var(--font-primary);
  margin-bottom: 8px;
}

.about-credential-description {
  font-size: 0.95rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  line-height: 1.4;
}

/* Journeys Section - Refined */
.about-journeys-refined {
  padding: 100px 0;
  background: #fdfcf8;
}

.about-journeys-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-journeys-header-refined {
  text-align: center;
  margin-bottom: 60px;
}

.about-journeys-title-refined {
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 32px 0;
  line-height: 1.2;
}

.about-journeys-ornament {
  width: 100px;
  height: 1px;
  background: var(--temple-gold);
  margin: 0 auto 32px auto;
  opacity: 0.6;
  position: relative;
}

.about-journeys-ornament::before,
.about-journeys-ornament::after {
  content: '';
  position: absolute;
  top: -3px;
  width: 8px;
  height: 8px;
  background: var(--temple-gold);
  border-radius: 50%;
}

.about-journeys-ornament::before {
  left: -15px;
}

.about-journeys-ornament::after {
  right: -15px;
}

.about-journeys-description-refined {
  font-size: 1.2rem;
  color: var(--stone);
  line-height: 1.8;
  font-family: var(--font-secondary);
  max-width: 800px;
  margin: 0 auto;
}

.about-journeys-grid-refined {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
}

.about-journey-card-refined {
  background: white;
  padding: 3rem;
  border-radius: 2px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.about-journey-card-refined::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--temple-gold);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.about-journey-card-refined:hover::before {
  transform: translateX(0);
}

.about-journey-card-refined:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.about-journey-header {
  margin-bottom: 24px;
}

.about-journey-title-refined {
  font-family: var(--font-primary);
  font-size: 1.8rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.about-journey-subtitle {
  font-size: 1rem;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-style: italic;
}

.about-journey-content {
  margin-top: 24px;
}

.about-journey-description-refined {
  font-size: 1.1rem;
  color: var(--stone);
  line-height: 1.7;
  margin-bottom: 24px;
  font-family: var(--font-secondary);
}

.about-journey-link-refined {
  color: var(--temple-gold);
  text-decoration: none;
  font-weight: 500;
  font-family: var(--font-secondary);
  font-size: 1rem;
  letter-spacing: 0.02em;
  transition: all 0.3s ease;
  position: relative;
  padding-bottom: 2px;
}

.about-journey-link-refined::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--temple-gold);
  transition: width 0.3s ease;
}

.about-journey-link-refined:hover::after {
  width: 100%;
}

/* CTA Section - Refined */
.about-cta-refined {
  padding: 100px 0;
  background: var(--whisper);
  position: relative;
  overflow: hidden;
}

.about-cta-refined::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23c19b68' fill-opacity='0.02'%3E%3Cpath d='M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm20 0c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.about-cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  position: relative;
  z-index: 2;
}

.about-cta-content-refined {
  text-align: center;
  background: white;
  padding: 4rem 3rem;
  border-radius: 2px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
  position: relative;
}

.about-cta-ornament {
  width: 60px;
  height: 1px;
  background: var(--temple-gold);
  margin: 0 auto 32px auto;
  opacity: 0.6;
  position: relative;
}

.about-cta-ornament::before,
.about-cta-ornament::after {
  content: '';
  position: absolute;
  top: -2px;
  width: 5px;
  height: 5px;
  background: var(--temple-gold);
  border-radius: 50%;
}

.about-cta-ornament::before {
  left: -10px;
}

.about-cta-ornament::after {
  right: -10px;
}

.about-cta-title-refined {
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 20px 0;
  line-height: 1.2;
}

.about-cta-description-refined {
  font-size: 1.2rem;
  color: var(--stone);
  margin: 0 0 40px 0;
  line-height: 1.6;
  font-family: var(--font-secondary);
}

.about-cta-button-refined {
  display: inline-block;
  padding: 18px 40px;
  background: transparent;
  border: 2px solid var(--temple-gold);
  color: var(--temple-gold);
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.about-cta-button-refined::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  z-index: -1;
}

.about-cta-button-refined:hover::before {
  left: 0;
}

.about-cta-button-refined:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(193, 155, 104, 0.4);
}

/* NOWY UKŁAD WYŚRODKOWANY - OPCJA 1 (Old Money Elegance) */

/* Main Content - Centered Layout */
.about-content-centered {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    #fdfcf8 0%,
    #f9f7f2 100%
  );
  position: relative;
  z-index: 5;
}

.about-content-container-centered {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Małe wyśrodkowane zdjęcie */
.about-photo-centered {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50px;
  width: 100%;
}

.about-photo-frame-small {
  position: relative;
  padding: 16px;
  background: white;
  border-radius: 50%;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 16px 64px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.about-photo-frame-small:hover {
  transform: scale(1.05);
  box-shadow: 
    0 6px 30px rgba(0, 0, 0, 0.12),
    0 12px 60px rgba(0, 0, 0, 0.06);
}

.about-photo-placeholder-small {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    #f9f7f2 0%,
    #f5f3ef 100%
  );
  border: 1px solid rgba(193, 155, 104, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.about-photo-placeholder-small .about-photo-content {
  color: var(--temple-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: relative;
}

.about-photo-placeholder-small .about-photo-ornament {
  display: none; /* Ukryj ornamenty w małym zdjęciu */
}

.about-photo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  background: radial-gradient(circle, rgba(193, 155, 104, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.about-photo-frame-small:hover .about-photo-glow {
  opacity: 1;
}

/* Tekst wyśrodkowany */
.about-text-centered {
  margin-bottom: 80px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.about-text-content-centered {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  padding: 60px;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  align-items: start;
}

.about-text-paragraph-centered {
  font-size: 1.25rem;
  line-height: 1.8;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-weight: 300;
  margin: 0 0 32px 0;
  text-align: left;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.about-text-paragraph-centered:first-child {
  font-size: 1.35rem;
  color: var(--temple-gold);
  font-weight: 400;
  position: relative;
}

.about-text-paragraph-centered:hover {
  opacity: 1;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Kolumny tekstu - symetria */
.about-text-column-left,
.about-text-column-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  height: 100%;
}

.about-text-column-left .about-text-paragraph-centered,
.about-text-column-right .about-text-paragraph-centered {
  width: 100%;
  margin-bottom: 32px;
}

.about-text-column-left .about-text-paragraph-centered:last-child,
.about-text-column-right .about-text-paragraph-centered:last-child {
  margin-bottom: 0;
}

/* Journeys - Centered Single Card */
.about-journeys-centered {
  padding: 60px 0;
  background: linear-gradient(135deg, 
    var(--whisper) 0%,
    rgba(248, 246, 240, 0.95) 100%
  );
}

.about-journeys-container-centered {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-journeys-header-centered {
  text-align: center;
  margin-bottom: 40px;
}

.about-journeys-title-centered {
  font-family: var(--font-primary);
  font-size: 2.8rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.about-journeys-description-centered {
  font-size: 1.2rem;
  color: var(--stone);
  line-height: 1.8;
  font-family: var(--font-secondary);
  margin: 0 auto 40px auto;
  max-width: 500px;
}

.about-journeys-card-single {
  display: flex;
  justify-content: center;
}

.about-journey-card-centered {
  background: rgba(255, 255, 255, 0.9);
  padding: 3.5rem;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
  max-width: 550px;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.about-journey-card-centered::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--temple-gold);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.about-journey-card-centered:hover::before {
  transform: translateX(0);
}

.about-journey-card-centered:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.about-journey-header-centered {
  margin-bottom: 24px;
}

.about-journey-title-centered {
  font-family: var(--font-primary);
  font-size: 1.8rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.about-journey-subtitle-centered {
  font-size: 1rem;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-style: italic;
}

.about-journey-content-centered {
  margin-top: 24px;
}

.about-journey-description-centered {
  font-size: 1.1rem;
  color: var(--stone);
  line-height: 1.7;
  margin-bottom: 24px;
  font-family: var(--font-secondary);
}

.about-journey-button-centered {
  display: inline-block;
  padding: 16px 32px;
  background: transparent;
  border: 2px solid var(--temple-gold);
  color: var(--temple-gold);
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.about-journey-button-centered::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  z-index: -1;
}

.about-journey-button-centered:hover::before {
  left: 0;
}

.about-journey-button-centered:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(193, 155, 104, 0.4);
  letter-spacing: 0.08em;
}

/* Responsive Design - Enhanced */
@media (max-width: 1024px) {
  .about-content-grid {
    grid-template-columns: 1fr;
    gap: 4rem;
  }
  
  .about-photo-column {
    position: static;
    order: 1;
  }
  
  .about-text-column {
    order: 2;
    padding-top: 0;
  }
  
  .about-credentials-grid-refined {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .about-text-content-centered {
    padding: 40px;
    gap: 32px;
  }
  
  .about-journey-card-centered {
    padding: 2.5rem;
  }
  
  .about-credentials-grid-refined .about-credential-card:first-child {
    grid-column: 1 / -1;
    justify-self: center;
    max-width: 300px;
  }
  
  .about-journeys-grid-refined {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  /* Nowy układ wyśrodkowany - tablet */
  .about-text-content-centered {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 32px;
  }
}

@media (max-width: 768px) {
  .about-hero-name {
    font-size: 2.8rem;
  }
  
  .about-photo-frame {
    transform: rotate(0deg);
  }
  
  .about-photo-placeholder {
    width: 280px;
    height: 350px;
  }
  
  .about-credentials-title,
  .about-journeys-title-refined,
  .about-cta-title-refined {
    font-size: 2rem;
  }
  
  .about-credential-card,
  .about-journey-card-refined {
    padding: 2rem;
  }
  
  .about-cta-content-refined {
    padding: 3rem 2rem;
  }
  
  /* Nowy układ wyśrodkowany - responsive */
  .about-content-centered {
    padding: 40px 0;
  }
  
  .about-photo-placeholder-small {
    width: 80px;
    height: 80px;
  }
  
  .about-content-container-centered {
    padding: 0 20px;
  }
  
  .about-text-content-centered {
    grid-template-columns: 1fr;
    padding: 32px 24px;
    gap: 24px;
  }
  
  .about-text-paragraph-centered {
    font-size: 1.1rem;
  }
  
  .about-credentials-grid-refined {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .about-credentials-grid-refined .about-credential-card:first-child {
    grid-column: 1;
  }
  
  .about-journey-card-centered {
    padding: 2rem;
  }
  
  .about-journeys-centered {
    padding: 40px 0;
  }
  
  .about-journeys-title-centered {
    font-size: 2.2rem;
  }
  
  .about-text-paragraph-centered {
    font-size: 1.1rem;
  }
  
  .about-text-paragraph-centered:first-child {
    font-size: 1.2rem;
  }
  
  .about-journey-button-centered {
    padding: 16px 32px;
    font-size: 1rem;
    min-height: 44px;
  }
}

/* ===== ANIMATIONS FOR OLD MONEY ELEGANCE ===== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation delays for staggered effect */
.about-credential-card:nth-child(1) {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.about-credential-card:nth-child(2) {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.about-credential-card:nth-child(3) {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.about-photo-centered {
  animation: fadeInScale 1s ease-out 0.1s both;
}

.about-text-content-centered {
  animation: fadeInUp 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =============================================
   🎨 INTEGRACJA WIZUALNA - BAKASANA O MNIE
   Jednolite tło, minimalistyczny design
   ============================================= */

/* Resetuj wszystkie kontenery - usuń białe tła */
.about-page-integrated {
  background: linear-gradient(
    180deg, 
    #FAF8F5 0%, 
    #F5F0E8 100%
  );
  min-height: 100vh;
  position: relative;
}

/* Efekt płynnego przejścia między sekcjami */
.about-page-integrated section {
  position: relative;
}

.about-page-integrated section::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(250, 248, 245, 0.3),
    transparent
  );
  pointer-events: none;
  z-index: 1;
}

.about-page-integrated section:first-child::before {
  display: none;
}

/* Delikatne separatory zamiast białych boxów */
.content-separator {
  width: 60px;
  height: 1px;
  background: var(--enterprise-brown);
  margin: 40px auto;
  opacity: 0.5;
  position: relative;
}

.content-separator::before,
.content-separator::after {
  content: '';
  position: absolute;
  top: -1px;
  width: 3px;
  height: 3px;
  background: var(--enterprise-brown);
  border-radius: 50%;
}

.content-separator::before {
  left: -8px;
}

.content-separator::after {
  right: -8px;
}

/* HERO SECTION - Minimalistyczny */
.about-hero-minimal {
  padding: 120px 0 80px 0;
  background: none;
  text-align: center;
}

.about-hero-container-minimal {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-hero-content-minimal {
  background: none;
  padding: 0;
}

.about-hero-name-minimal {
  font-family: var(--font-primary);
  font-size: 3.5rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 20px 0;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.about-hero-credentials-minimal {
  font-size: 1.2rem;
  color: var(--stone);
  margin: 0 0 40px 0;
  font-family: var(--font-secondary);
  font-weight: 400;
}

.about-hero-quote-minimal {
  font-size: 1.5rem;
  color: var(--temple-gold);
  font-style: italic;
  font-family: var(--font-primary);
  font-weight: 300;
  margin: 0;
  opacity: 0.9;
}

/* MAIN CONTENT - Zintegrowane */
.about-content-integrated {
  padding: 80px 0;
  background: none;
}

.about-content-container-integrated {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

/* Zdjęcie - subtelne, zintegrowane */
.about-photo-integrated {
  display: flex;
  justify-content: center;
  margin-bottom: 60px;
}

.about-photo-frame-integrated {
  position: relative;
  padding: 12px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(190, 149, 97, 0.15);
  border-radius: 50%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.about-photo-frame-integrated:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.about-photo-placeholder-integrated {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--temple-gold);
}

/* Tekst - bez białego tła */
.about-text-integrated {
  margin-bottom: 80px;
}

.about-text-content-integrated {
  max-width: 1000px;
  margin: 0 auto;
  background: none;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.about-text-paragraph-integrated {
  font-size: 1.25rem;
  line-height: 1.8;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  font-weight: 300;
  margin: 0 0 32px 0;
  text-align: left;
  opacity: 0.9;
}

.about-text-paragraph-integrated:first-child {
  font-size: 1.35rem;
  color: var(--temple-gold);
  font-weight: 400;
}

/* SEKCJA MOJA HISTORIA */
.about-story-integrated {
  padding: 80px 0;
  background: none;
}

.about-story-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

.about-story-title {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  color: var(--charcoal);
  letter-spacing: 0.3em;
  margin: 0 0 20px 0;
  text-transform: uppercase;
}

.about-story-content {
  text-align: left;
  margin-top: 60px;
}

.about-story-paragraph {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  margin: 0 0 32px 0;
  opacity: 0.9;
}

/* CERTYFIKACJE - Minimalistyczne */
.about-credentials-integrated {
  padding: 80px 0;
  background: none;
}

.about-credentials-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

.about-credentials-title-integrated {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  color: var(--charcoal);
  letter-spacing: 0.3em;
  margin: 0 0 20px 0;
  text-transform: uppercase;
}

.about-credentials-list-integrated {
  margin-top: 60px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  text-align: left;
}

.about-credential-item-integrated {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  background: none;
  padding: 0;
  border: none;
  box-shadow: none;
}

.about-credential-marker {
  width: 3px;
  height: 3px;
  background: var(--temple-gold);
  border-radius: 50%;
  margin-top: 12px;
  flex-shrink: 0;
}

.about-credential-content-integrated {
  flex: 1;
}

.about-credential-label-integrated {
  font-size: 1.2rem;
  font-weight: 400;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  margin: 0 0 8px 0;
}

.about-credential-description-integrated {
  font-size: 1rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  opacity: 0.8;
}

/* PODRÓŻE - Zintegrowane */
.about-journeys-integrated {
  padding: 80px 0;
  background: none;
}

.about-journeys-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

.about-journeys-title-integrated {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  color: var(--charcoal);
  letter-spacing: 0.3em;
  margin: 0 0 20px 0;
  text-transform: uppercase;
}

.about-journeys-content-integrated {
  margin-top: 60px;
  text-align: left;
}

.about-journeys-description-integrated {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.about-journeys-destinations {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.about-destination-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-left: 20px;
  border-left: 3px solid var(--temple-gold);
}

.about-destination-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  min-width: 120px;
}

.about-destination-description {
  font-size: 1rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  opacity: 0.8;
}

.about-journey-button-integrated {
  display: inline-block;
  padding: 16px 0;
  background: none;
  border: none;
  border-bottom: 2px solid var(--temple-gold);
  color: var(--temple-gold);
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.about-journey-button-integrated:hover {
  border-bottom-color: var(--charcoal);
  color: var(--charcoal);
  transform: translateY(-2px);
}

/* SEKCJA JOGA ONLINE - Zintegrowana */
.about-online-integrated {
  padding: 80px 0;
  background: none;
}

.about-online-container-integrated {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
  text-align: center;
}

.about-online-title-integrated {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 400;
  color: var(--charcoal);
  letter-spacing: 0.3em;
  margin: 0 0 20px 0;
  text-transform: uppercase;
}

.about-online-content-integrated {
  margin-top: 60px;
  text-align: left;
}

.about-online-description-integrated {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  margin: 0 0 50px 0;
  opacity: 0.9;
  text-align: center;
}

.about-online-methods {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 50px;
}

.about-online-method-item {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 0;
  background: none;
  border: none;
  box-shadow: none;
}

.about-online-method-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(190, 149, 97, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--temple-gold);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.about-online-method-item:hover .about-online-method-icon {
  background: rgba(190, 149, 97, 0.2);
  transform: scale(1.1);
}

.about-online-method-content {
  flex: 1;
}

.about-online-method-title {
  font-size: 1.3rem;
  font-weight: 400;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  margin: 0 0 8px 0;
}

.about-online-method-description {
  font-size: 1.1rem;
  color: var(--stone);
  font-family: var(--font-secondary);
  font-weight: 300;
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

.about-online-platforms {
  margin-bottom: 40px;
  padding: 32px 0;
  border-top: 1px solid rgba(190, 149, 97, 0.2);
  border-bottom: 1px solid rgba(190, 149, 97, 0.2);
}

.about-online-platforms-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--charcoal);
  font-family: var(--font-secondary);
  margin: 0 0 20px 0;
  text-align: center;
}

.about-online-platforms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
}

.about-online-platform-item {
  padding: 8px 16px;
  background: rgba(190, 149, 97, 0.1);
  border: 1px solid rgba(190, 149, 97, 0.2);
  border-radius: 20px;
  font-size: 0.9rem;
  color: var(--temple-gold);
  font-family: var(--font-secondary);
  font-weight: 400;
  transition: all 0.3s ease;
}

.about-online-platform-item:hover {
  background: rgba(190, 149, 97, 0.2);
  transform: translateY(-2px);
}

.about-online-button-integrated {
  display: inline-block;
  padding: 16px 0;
  background: none;
  border: none;
  border-bottom: 2px solid var(--temple-gold);
  color: var(--temple-gold);
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  margin-top: 20px;
  text-align: center;
  width: 100%;
}

.about-online-button-integrated:hover {
  border-bottom-color: var(--charcoal);
  color: var(--charcoal);
  transform: translateY(-2px);
}

/* CTA SECTION - Minimalistyczny */
.about-cta-integrated {
  padding: 80px 0;
  background: none;
}

.about-cta-container-integrated {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.about-cta-content-integrated {
  text-align: center;
  background: none;
  padding: 0;
}

.about-cta-title-integrated {
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 300;
  color: var(--charcoal);
  margin: 20px 0;
  line-height: 1.2;
}

.about-cta-description-integrated {
  font-size: 1.2rem;
  color: var(--stone);
  margin: 0 0 40px 0;
  line-height: 1.6;
  font-family: var(--font-secondary);
  font-weight: 300;
}

.about-cta-button-integrated {
  display: inline-block;
  padding: 18px 40px;
  background: transparent;
  border: 2px solid var(--temple-gold);
  color: var(--temple-gold);
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.about-cta-button-integrated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--temple-gold);
  transition: left 0.4s ease;
  z-index: -1;
}

.about-cta-button-integrated:hover::before {
  left: 0;
}

.about-cta-button-integrated:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(193, 155, 104, 0.4);
}

/* RESPONSIVE DESIGN */
@media (max-width: 1024px) {
  .about-text-content-integrated {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .about-journeys-destinations {
    gap: 16px;
  }
  
  .about-destination-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .about-destination-name {
    min-width: auto;
  }
  
  .about-online-methods {
    gap: 28px;
  }
  
  .about-online-platforms-list {
    gap: 14px;
  }
}

@media (max-width: 768px) {
  .about-hero-name-minimal {
    font-size: 2.8rem;
  }
  
  .about-hero-credentials-minimal {
    font-size: 1.1rem;
  }
  
  .about-hero-quote-minimal {
    font-size: 1.3rem;
  }
  
  .about-text-paragraph-integrated {
    font-size: 1.1rem;
  }
  
  .about-text-paragraph-integrated:first-child {
    font-size: 1.2rem;
  }
  
  .about-story-paragraph {
    font-size: 1.1rem;
  }
  
  .about-journeys-description-integrated {
    font-size: 1.1rem;
  }
  
  .about-cta-title-integrated {
    font-size: 2rem;
  }
  
  .about-credentials-list-integrated {
    gap: 24px;
  }
  
  .about-credential-item-integrated {
    gap: 16px;
  }
  
  .about-credential-label-integrated {
    font-size: 1.1rem;
  }
  
  .about-destination-item {
    padding-left: 16px;
  }
  
  .about-online-methods {
    gap: 24px;
  }
  
  .about-online-method-item {
    gap: 16px;
  }
  
  .about-online-method-icon {
    width: 40px;
    height: 40px;
  }
  
  .about-online-method-title {
    font-size: 1.2rem;
  }
  
  .about-online-method-description {
    font-size: 1rem;
  }
  
  .about-online-platforms-list {
    gap: 12px;
  }
  
  .about-online-platform-item {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
  
  .about-online-description-integrated {
    font-size: 1.1rem;
  }
}

/* Subtelne animacje wejścia */
.about-content-integrated,
.about-story-integrated,
.about-credentials-integrated,
.about-journeys-integrated,
.about-online-integrated,
.about-cta-integrated {
  animation: fadeInUp 0.8s ease-out both;
}

.about-story-integrated {
  animation-delay: 0.1s;
}

.about-credentials-integrated {
  animation-delay: 0.2s;
}

.about-journeys-integrated {
  animation-delay: 0.3s;
}

.about-online-integrated {
  animation-delay: 0.4s;
}

.about-cta-integrated {
  animation-delay: 0.5s;
}

/* Efekty hover dla paragrafów */
.about-text-paragraph-integrated:hover,
.about-story-paragraph:hover,
.about-journeys-description-integrated:hover,
.about-online-description-integrated:hover {
  opacity: 1;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Efekty hover dla elementów kwalifikacji */
.about-credential-item-integrated:hover {
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.about-credential-item-integrated:hover .about-credential-marker {
  transform: scale(1.5);
  transition: all 0.3s ease;
}

/* Efekty hover dla destynacji */
.about-destination-item:hover {
  transform: translateX(10px);
  border-left-color: var(--charcoal);
  transition: all 0.3s ease;
}

.about-destination-item:hover .about-destination-name {
  color: var(--temple-gold);
  transition: all 0.3s ease;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Apply animations to key elements */
.about-hero-content-elegant {
  animation: fadeInUp 0.8s ease-out;
}

.about-photo-frame {
  animation: fadeInLeft 0.8s ease-out 0.2s both;
}

.about-text-content {
  animation: fadeInRight 0.8s ease-out 0.4s both;
}

.about-credential-card {
  animation: scaleIn 0.6s ease-out both;
}

.about-credential-card:nth-child(1) {
  animation-delay: 0.1s;
}

.about-credential-card:nth-child(2) {
  animation-delay: 0.2s;
}

.about-credential-card:nth-child(3) {
  animation-delay: 0.3s;
}

.about-journey-card-refined {
  animation: fadeInUp 0.6s ease-out both;
}

.about-journey-card-refined:nth-child(1) {
  animation-delay: 0.1s;
}

.about-journey-card-refined:nth-child(2) {
  animation-delay: 0.2s;
}

.about-cta-content-refined {
  animation: scaleIn 0.8s ease-out 0.3s both;
}

/* Reduce animations on mobile for better performance */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Hover effects for enhanced interactivity */
.about-hero-name:hover {
  color: var(--temple-gold);
  transition: color 0.3s ease;
}

.about-text-paragraph:hover {
  color: var(--charcoal);
  transition: color 0.3s ease;
}

/* Subtle parallax effect for ornaments */
.about-hero-ornament {
  transition: transform 0.3s ease;
}

.about-hero-refined:hover .about-hero-ornament {
  transform: translateY(-2px);
}

.about-journeys-ornament {
  transition: transform 0.3s ease;
}

.about-journeys-header-refined:hover .about-journeys-ornament {
  transform: translateY(-1px);
}

/* Animacje dla sekcji O mnie */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Subtelne animacje dla ornamentów */
.about-hero-ornament {
  animation: gentleFloat 4s ease-in-out infinite;
}

.about-journeys-ornament {
  animation: gentleFloat 6s ease-in-out infinite;
}

/* ===== MAGAZINE STYLE BLOG SECTION ===== */

/* Magazine Hero Section */
.magazine-hero {
  padding: 80px 0 60px 0;
  max-width: var(--container-max);
  margin: 0 auto;
  text-align: center;
}

.magazine-hero-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.magazine-header-line {
  width: 60px;
  height: 1px;
  background: var(--temple-gold);
  margin: 0 auto 32px auto;
  opacity: 0.6;
}

.magazine-title {
  font-family: var(--font-primary);
  font-size: 48px;                     /* Znacznie mniejszy niż poprzedni */
  font-weight: 300;
  color: var(--charcoal);
  letter-spacing: 0.05em;
  margin: 0 0 24px 0;
  text-transform: none;                /* Bez uppercase */
}

.magazine-subtitle {
  font-family: var(--font-secondary);
  font-size: 16px;
  font-weight: 300;
  color: var(--stone);
  margin: 0 0 32px 0;
  font-style: italic;
}

.magazine-meta {
  font-family: var(--font-secondary);
  font-size: 12px;
  font-weight: 300;
  color: var(--stone);
  opacity: 0.7;
  letter-spacing: 0.1em;
  margin-bottom: 32px;
}

/* Magazine Content Layout */
.magazine-content {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--element-breathing);
}

.magazine-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  margin-bottom: 80px;
}

/* Featured Article - Large */
.magazine-featured {
  grid-row: span 2;
}

/* Secondary Articles */
.magazine-secondary {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* Small Articles Grid */
.magazine-grid-small {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

/* Magazine Card Styles */
.magazine-card,
.magazine-card-featured {
  background: var(--pure-white);
  border: 1px solid rgba(139, 134, 128, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.magazine-card:hover,
.magazine-card-featured:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42, 39, 36, 0.08);
}

.magazine-card-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

/* Image Sections */
.magazine-card-image {
  position: relative;
  overflow: hidden;
}

.magazine-card .magazine-card-image {
  height: 200px;
}

.magazine-card-featured .magazine-card-image {
  height: 300px;
}

.magazine-image-bg {
  width: 100%;
  height: 100%;
  transition: transform 0.4s ease;
  filter: sepia(0.1) contrast(0.95);
}

.magazine-card:hover .magazine-image-bg,
.magazine-card-featured:hover .magazine-image-bg {
  transform: scale(1.02);
}

.magazine-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(42, 39, 36, 0.1) 100%);
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.magazine-category {
  font-family: var(--font-secondary);
  font-size: 10px;
  font-weight: 400;
  color: var(--pure-white);
  background: rgba(42, 39, 36, 0.7);
  padding: 6px 12px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  backdrop-filter: blur(10px);
}

/* Content Sections */
.magazine-card-content {
  padding: 32px;
}

.magazine-card-featured .magazine-card-content {
  padding: 40px;
}

.magazine-card-title {
  font-family: var(--font-primary);
  font-size: 20px;
  font-weight: 400;
  color: var(--charcoal);
  line-height: 1.4;
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
}

.magazine-card-featured .magazine-card-title {
  font-size: 28px;
  margin-bottom: 20px;
}

.magazine-card-link:hover .magazine-card-title {
  color: var(--temple-gold);
}

.magazine-card-excerpt {
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  color: var(--stone);
  line-height: 1.6;
  margin: 0 0 24px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.magazine-card-featured .magazine-card-excerpt {
  font-size: 15px;
  -webkit-line-clamp: 4;
  margin-bottom: 32px;
}

.magazine-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.magazine-read-more {
  font-family: var(--font-secondary);
  font-size: 11px;
  font-weight: 400;
  color: var(--temple-gold);
  letter-spacing: 0.1em;
  text-transform: uppercase;
  transition: opacity 0.3s ease;
}

.magazine-card-link:hover .magazine-read-more {
  opacity: 0.7;
}

.magazine-read-time {
  font-family: var(--font-secondary);
  font-size: 11px;
  font-weight: 300;
  color: var(--stone);
  opacity: 0.6;
}

/* Empty State */
.magazine-empty {
  text-align: center;
  padding: 120px 0;
}

.magazine-empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.magazine-empty-title {
  font-family: var(--font-primary);
  font-size: 24px;
  font-weight: 300;
  color: var(--charcoal);
  margin: 0 0 16px 0;
}

.magazine-empty-text {
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 300;
  color: var(--stone);
  line-height: 1.6;
}

/* Magazine Responsive Styles */
@media (max-width: 1024px) {
  .magazine-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .magazine-secondary {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .magazine-grid-small {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .magazine-title {
    font-size: 36px;
  }
  
  .magazine-hero {
    padding: 60px 0 40px 0;
  }
  
  .magazine-content {
    padding: 0 5%;
  }
  
  .magazine-secondary {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .magazine-grid-small {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .magazine-card-content,
  .magazine-card-featured .magazine-card-content {
    padding: 24px;
  }
  
  .magazine-card .magazine-card-image,
  .magazine-card-featured .magazine-card-image {
    height: 180px;
  }
}

@media (max-width: 480px) {
  .magazine-title {
    font-size: 28px;
  }
  
  .magazine-subtitle {
    font-size: 14px;
  }
  
  .magazine-meta {
    font-size: 11px;
  }
  
  .magazine-card-content,
  .magazine-card-featured .magazine-card-content {
    padding: 20px;
  }
  
  .magazine-card-title,
  .magazine-card-featured .magazine-card-title {
    font-size: 18px;
  }
  
  .magazine-card-excerpt,
  .magazine-card-featured .magazine-card-excerpt {
    font-size: 13px;
  }
  
  /* Enhanced mobile responsiveness */
  .hero-title {
    font-size: clamp(48px, 12vw, 72px);
    letter-spacing: 0.12em;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 13px;
    margin-top: 24px;
  }

  .hero-quote {
    font-size: 15px;
    margin: 24px 0;
  }

  .section-header {
    font-size: clamp(28px, 7vw, 36px);
    margin-bottom: 48px;
  }

  .destinations-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 4%;
  }

  .destination-card {
    margin-bottom: 24px;
  }

  .footer-links {
    flex-direction: column;
    gap: 20px;
  }

  .section {
    padding: 80px 0;
  }

  .container {
    padding: 80px 6%;
  }

  .section-divider {
    width: 80px;
    margin: 60px auto;
  }

  .btn-ghost {
    padding: 16px 32px;
    font-size: 11px;
  }
}

/* ===== ENHANCED UTILITY CLASSES ===== */
.text-balance {
  text-wrap: balance;
}

.smooth-scroll {
  scroll-behavior: auto; /* Changed for better performance */
}

.backdrop-blur-subtle {
  backdrop-filter: blur(8px);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.elegant-shadow {
  box-shadow: 
    0 1px 3px rgba(44, 41, 40, 0.02),
    0 4px 12px rgba(44, 41, 40, 0.04),
    0 16px 24px rgba(44, 41, 40, 0.02);
}

.elegant-shadow-hover {
  box-shadow: 
    0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08),
    0 24px 48px rgba(44, 41, 40, 0.04);
}

.gradient-text {
  background: linear-gradient(135deg, var(--temple-gold), var(--om-symbol));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.spiritual-ornament {
  position: relative;
}

.spiritual-ornament::before {
  content: '॰';
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--temple-gold);
  font-size: 18px;
  opacity: 0.6;
}

.micro-interaction {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-interaction:hover {
  transform: translateY(-1px);
  filter: brightness(1.05);
}

/* ===== ENHANCED COMPONENT CONSISTENCY ===== */
.enterprise-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enterprise-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(253, 249, 243, 0.8) 0%,
    rgba(249, 246, 242, 0.9) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.enterprise-card:hover::before {
  opacity: 1;
}

.enterprise-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 4px 16px rgba(44, 41, 40, 0.06),
    0 12px 32px rgba(44, 41, 40, 0.08);
}

/* ===== SMOOTH SECTION TRANSITIONS ===== */
.section-transition {
  position: relative;
  overflow: hidden;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  pointer-events: none;
  z-index: -1;
}

.section-transition::after {
  content: '';
  position: absolute;
  bottom: -60px;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(
    to top,
    transparent 0%,
    rgba(253, 249, 243, 0.3) 30%,
    rgba(253, 249, 243, 0.7) 60%,
    rgba(253, 249, 243, 1) 100%
  );
  pointer-events: none;
  z-index: -1;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ENHANCED ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible-only {
  outline: none;
}

.focus-visible-only:focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .navigation,
  .footer,
  .btn-ghost,
  .social-links {
    display: none !important;
  }

  .section {
    padding: 20px 0;
  }

  .hero-title {
    font-size: 36px;
  }
}

/* =============================================
   🎯 NOWE SEKCJE - PLAN NAPRAWCZY
   ============================================= */

/* ===== SEKCJA MIEJSCA GDZIE ODDYCHASZ ===== */
.destinations-section {
  background: linear-gradient(180deg, #FDF9F3 0%, #FAF5F0 100%);
  padding: 120px 0;
}

.destinations-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  max-width: 1000px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .destinations-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

.destination-card {
  background: white;
  box-shadow: 0 10px 40px rgba(0,0,0,0.05);
  overflow: hidden;
  transition: all 0.4s ease;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

.destination-image-wrapper {
  height: 400px;
  overflow: hidden;
}

.destination-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.destination-card:hover .destination-image {
  transform: scale(1.05);
}

.card-content {
  padding: 40px;
}

.destination-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 36px;
  font-weight: 300;
  margin-bottom: 16px;
  color: var(--charcoal);
}

.destination-meta {
  color: var(--temple-gold);
  font-size: 14px;
  letter-spacing: 0.1em;
  margin-bottom: 24px;
  text-transform: uppercase;
}

.destination-description {
  font-size: 18px;
  line-height: 1.6;
  color: var(--charcoal);
  margin-bottom: 20px;
}

.destination-testimonial {
  font-style: italic;
  color: var(--stone);
  font-size: 14px;
  margin-bottom: 24px;
}

.destination-link {
  display: inline-block;
  color: var(--temple-gold);
  font-size: 14px;
  text-decoration: none;
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
}

.destination-link:hover {
  color: var(--charcoal);
}

/* ===== SEKCJA O JULII (UPROSZCZONA) ===== */
.about-julia-simple {
  background: var(--pure-white);
  padding: 100px 0;
}

.about-julia-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 0 8%;
}

.julia-portrait-wrapper {
  margin-bottom: 40px;
}

.julia-portrait {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto;
}

.julia-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 48px;
  font-weight: 200;
  margin-bottom: 20px;
  color: var(--charcoal);
}

.divider-gold {
  width: 60px;
  height: 2px;
  background: var(--temple-gold);
  margin: 0 auto 40px;
}

.julia-bio {
  font-size: 18px;
  line-height: 1.8;
  color: var(--charcoal-light);
  margin-bottom: 40px;
}

.julia-bio p {
  margin-bottom: 24px;
}

.text-gold {
  color: var(--temple-gold);
}

.julia-cta {
  display: inline-block;
  border: 1px solid var(--temple-gold);
  color: var(--temple-gold);
  padding: 16px 48px;
  font-size: 14px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  text-decoration: none;
  transition: all 0.3s ease;
}

.julia-cta:hover {
  background: var(--temple-gold);
  color: white;
  transform: translateY(-2px);
}

/* ===== SEKCJA KONTAKT (MINIMALISTYCZNY) ===== */
.contact-simple {
  background: linear-gradient(135deg, #FAF5F0 0%, #F5EDE4 100%);
  padding: 100px 0;
}

.contact-container {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  padding: 0 8%;
}

.contact-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 56px;
  font-weight: 200;
  margin-bottom: 20px;
  color: var(--charcoal);
}

.contact-subtitle {
  font-size: 20px;
  color: var(--temple-gold);
  margin-bottom: 60px;
  font-style: italic;
}

.contact-options {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-bottom: 40px;
}

@media (max-width: 768px) {
  .contact-options {
    flex-direction: column;
    gap: 20px;
  }
}

.contact-button {
  flex: 1;
  padding: 20px 40px;
  background: white;
  border: 1px solid var(--stone-light);
  font-size: 16px;
  text-decoration: none;
  color: var(--charcoal);
  transition: all 0.3s ease;
  display: block;
}

.contact-button:hover {
  border-color: var(--temple-gold);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.contact-button h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 400;
}

.contact-button p {
  margin: 0;
  font-size: 14px;
  color: var(--stone);
}

/* ===== FOOTER ULTRA MINIMALISTYCZNY - ELEGANT EDITION ===== */
.footer {
  background: var(--soft-black); /* Nie czarny, cieplejszy */
  color: var(--whisper);
  padding: 60px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* Subtelny pattern/tekstura */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(
      circle at 20% 50%, 
      rgba(184,149,106,0.03) 0%, 
      transparent 50%
    );
  pointer-events: none;
}

/* Delikatny separator na górze */
.footer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(184,149,106,0.3),
    transparent
  );
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 8%;
}

.logo {
  font-family: 'Cormorant Garamond', serif;
  font-size: 24px;
  margin-bottom: 20px;
  color: var(--whisper);
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 30px 0;
}

@media (max-width: 768px) {
  .footer-links {
    flex-direction: column;
    gap: 20px;
  }
}

.footer-link {
  color: var(--whisper);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--temple-gold);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
}

.social-icon {
  color: var(--whisper);
  transition: color 0.3s ease;
}

.social-icon:hover {
  color: var(--temple-gold);
}

.copyright {
  font-size: 12px;
  opacity: 0.6;
  margin-top: 30px;
}

/* ===== GHOST BUTTONS - UNIVERSAL ELEGANT STYLE ===== */
.btn-ghost {
  background: transparent;
  border: 1.5px solid #B8956A;
  color: #B8956A;
  padding: 12px 30px;
  border-radius: 0;
  transition: all 0.3s ease;
  font-weight: 300;
  letter-spacing: 1px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.btn-ghost:hover {
  background: #B8956A;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(184, 149, 106, 0.2);
}

/* For dark sections - global ghost button */
.dark-section .btn-ghost {
  border-color: rgba(255,255,255,0.7);
  color: rgba(255,255,255,0.9);
}

.dark-section .btn-ghost:hover {
  background: rgba(255,255,255,0.1);
  border-color: rgba(255,255,255,0.9);
  color: white;
}

/* ===== FLOATING ANIMATION ===== */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% { 
    transform: translateY(-10px);
    opacity: 0.8;
  }
}

/* ===== GLOBALNE POPRAWKI ===== */
* {
  transition: all 0.3s ease;
}

.section-divider {
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--temple-gold), transparent);
  margin: 60px auto;
}

/* ===== SPÓJNA HIERARCHIA FONTÓW ===== */
/* Hero title - najważniejszy */
.hero-title {
  font-size: clamp(120px, 10vw, 140px);
}

/* Section headers - główne nagłówki sekcji */
.section-header {
  font-size: clamp(48px, 6vw, 56px);
}

/* Subsection headers - podsekcje */
.subsection-header {
  font-size: clamp(32px, 4vw, 36px);
}

/* Body text - tekst główny */
.body-text {
  font-size: clamp(16px, 1.5vw, 18px);
}

/* Small text - drobny tekst */
.small-text {
  font-size: clamp(12px, 1vw, 14px);
}

/* ===== KOLORY - TYLKO DOZWOLONE ===== */
.text-main { color: #FDF9F3; }
.text-section { color: #FAF5F0; }
.text-primary { color: var(--charcoal); }
.text-accent { color: var(--temple-gold); }
.text-footer { color: #2A2520; }

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .destinations-section {
    padding: 80px 0;
  }
  
  .about-julia-simple {
    padding: 80px 0;
  }
  
  .contact-simple {
    padding: 80px 0;
  }
  
  .destination-title {
    font-size: 28px;
  }
  
  .julia-title {
    font-size: 36px;
  }
  
  .contact-title {
    font-size: 42px;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.destination-card,
.julia-portrait,
.contact-button {
  animation: fadeInUp 0.8s ease-out;
}