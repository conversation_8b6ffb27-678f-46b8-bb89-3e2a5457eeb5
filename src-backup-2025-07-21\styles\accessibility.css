/**
 * ♿ BAKASANA - COMPREHENSIVE ACCESSIBILITY STYLES
 * WCAG 2.1 AA Compliant Design System
 * 
 * Features:
 * - Color contrast compliance (4.5:1 normal, 3:1 large text)
 * - Focus indicators with 3px minimum
 * - High contrast mode
 * - Color blind support
 * - Dyslexia-friendly typography
 * - Reading mode optimizations
 * - Reduced motion support
 * - Screen reader optimizations
 */

/* ===== CORE ACCESSIBILITY UTILITIES ===== */

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Skip links for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: #000000;
  color: #ffffff;
  padding: 8px 16px;
  text-decoration: none;
  font-family: var(--font-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 0;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 3px solid #ff6600;
  outline-offset: 2px;
}

/* ===== FOCUS INDICATORS ===== */

/* Enhanced focus indicators for WCAG compliance */
*:focus-visible {
  outline: 3px solid #ff6600 !important;
  outline-offset: 2px !important;
  border-radius: 2px !important;
}

/* Focus indicators for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
[role="button"]:focus-visible,
[role="link"]:focus-visible,
[tabindex]:focus-visible {
  outline: 3px solid #ff6600 !important;
  outline-offset: 2px !important;
}

/* Enhanced focus mode */
[data-focus-mode="true"] *:focus-visible {
  outline-width: var(--focus-outline-width, 4px) !important;
  outline-style: var(--focus-outline-style, solid) !important;
  outline-color: var(--focus-outline-color, #ff6600) !important;
  outline-offset: var(--focus-outline-offset, 3px) !important;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 5px #ff6600 !important;
}

/* ===== HIGH CONTRAST MODE ===== */

[data-high-contrast="true"] {
  --text-color: #000000 !important;
  --background-color: #ffffff !important;
  --accent-color: #0066cc !important;
  --border-color: #000000 !important;
  --link-color: #0066cc !important;
  --focus-color: #ff6600 !important;
  --error-color: #cc0000 !important;
  --success-color: #006600 !important;
}

[data-high-contrast="true"] * {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-high-contrast="true"] a,
[data-high-contrast="true"] [role="link"] {
  color: var(--link-color) !important;
  text-decoration: underline !important;
}

[data-high-contrast="true"] button,
[data-high-contrast="true"] [role="button"] {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  border: 2px solid var(--border-color) !important;
}

[data-high-contrast="true"] input,
[data-high-contrast="true"] textarea,
[data-high-contrast="true"] select {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  border: 2px solid var(--border-color) !important;
}

/* ===== COLOR BLIND SUPPORT ===== */

[data-color-blind-mode="protanopia"] {
  filter: url(#protanopia-filter);
}

[data-color-blind-mode="deuteranopia"] {
  filter: url(#deuteranopia-filter);
}

[data-color-blind-mode="tritanopia"] {
  filter: url(#tritanopia-filter);
}

[data-color-blind-mode="monochromacy"] {
  filter: grayscale(100%);
}

/* ===== DYSLEXIA MODE ===== */

[data-dyslexia-mode="true"] {
  --font-family-override: 'OpenDyslexic', Arial, sans-serif;
  --letter-spacing-override: 0.12em;
  --line-height-override: 1.8;
}

[data-dyslexia-mode="true"] * {
  font-family: var(--font-family-override) !important;
  letter-spacing: var(--letter-spacing-override) !important;
  line-height: var(--line-height-override) !important;
}

[data-dyslexia-mode="true"] p,
[data-dyslexia-mode="true"] div,
[data-dyslexia-mode="true"] span {
  word-spacing: 0.16em !important;
}

/* ===== READING MODE ===== */

[data-reading-mode="true"] {
  --reading-width: 65ch;
  --reading-line-height: 1.8;
  --reading-font-size: 1.1em;
}

[data-reading-mode="true"] main,
[data-reading-mode="true"] article,
[data-reading-mode="true"] .content {
  max-width: var(--reading-width) !important;
  margin: 0 auto !important;
  line-height: var(--reading-line-height) !important;
  font-size: var(--reading-font-size) !important;
}

[data-reading-mode="true"] p {
  margin-bottom: 1.5em !important;
}

/* ===== REDUCED MOTION SUPPORT ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Force reduced motion when user preference is set */
[data-reduced-motion="true"] *,
[data-reduced-motion="true"] *::before,
[data-reduced-motion="true"] *::after {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* ===== KEYBOARD NAVIGATION ===== */

/* Show focus indicators only during keyboard navigation */
[data-keyboard-navigation="true"] *:focus {
  outline: 3px solid #ff6600 !important;
  outline-offset: 2px !important;
}

/* Hide focus indicators during mouse navigation */
[data-keyboard-navigation="false"] *:focus {
  outline: none;
}

/* ===== RESPONSIVE FONT SCALING ===== */

html {
  font-size: var(--base-font-size, 16px);
}

/* Scale all text elements proportionally */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
input, textarea, select,
label, li {
  font-size: calc(1em * var(--font-scale, 1));
}

/* ===== TOUCH TARGET SIZES ===== */

/* Ensure minimum 44px touch targets for mobile */
button,
a,
input[type="button"],
input[type="submit"],
input[type="reset"],
[role="button"],
[role="link"] {
  min-height: 44px;
  min-width: 44px;
  padding: 8px 12px;
}

/* ===== ERROR AND SUCCESS STATES ===== */

.error,
[aria-invalid="true"] {
  border-color: var(--error-color, #cc0000) !important;
  color: var(--error-color, #cc0000) !important;
}

.success,
[aria-invalid="false"] {
  border-color: var(--success-color, #006600) !important;
}

/* ===== LIVE REGIONS ===== */

[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* ===== PROGRESSIVE ENHANCEMENT ===== */

/* Default styles for no-js */
.no-js .js-only {
  display: none !important;
}

.js .no-js-only {
  display: none !important;
}

/* Mobile menu fallbacks */
.no-js .mobile-menu-toggle {
  display: none !important;
}

.no-js .navigation-list {
  display: block !important;
}

/* Form enhancements */
.no-js .progressive-form {
  /* Ensure forms work without JS */
}

.no-js .modal-overlay {
  /* Convert modals to inline content */
  position: static !important;
  background: transparent !important;
}

.no-js .lightbox {
  display: none !important;
}

/* ===== PRINT STYLES ===== */

@media print {
  .skip-link,
  .accessibility-toolbar,
  .js-only,
  [aria-hidden="true"] {
    display: none !important;
  }

  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  a::after {
    content: " (" attr(href) ")";
  }

  .no-js-only {
    display: block !important;
  }
}
