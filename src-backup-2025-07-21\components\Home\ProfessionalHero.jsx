'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const ProfessionalHero = () => {

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-sanctuary">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0 overflow-hidden opacity-20">
        <div className="absolute w-96 h-96 bg-temple-gold/10 rectangular blur-3xl top-1/4 left-1/4 animate-pulse"></div>
        <div className="absolute w-80 h-80 bg-sage-green/10 rectangular blur-3xl bottom-1/4 right-1/4 animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Background Image - More sharp than retreat page */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi Bali i Sri Lanka"
          fill
          className="object-cover object-center opacity-25"
          priority
          sizes="100vw"
          quality={90}
        />
        <div className="absolute inset-0 bg-gradient-to-b from-sanctuary/70 via-sanctuary/40 to-sanctuary/80" />
      </div>

      {/* Main Content */}
      <div className="relative z-20 text-center max-w-5xl mx-auto px-6 lg:px-8">
        <div className="space-y-8">
          {/* Professional Badge */}
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-temple-gold/8 backdrop-blur-sm border border-temple-gold/12">
            <span className="w-1.5 h-1.5 bg-temple-gold rectangular"></span>
            <span className="text-xs font-secondary text-charcoal tracking-wider uppercase font-medium">Retreaty Jogi • Bali & Sri Lanka</span>
          </div>

          {/* Main Title */}
          <h1 className="font-primary text-6xl md:text-8xl font-light text-charcoal leading-tight tracking-wide">
            BAKASANA
          </h1>

          {/* Tagline */}
          <p className="text-lg md:text-xl text-charcoal/70 font-primary italic mb-8 font-light">
            — joga, retreaty, równowaga —
          </p>

          {/* Description */}
          <p className="text-base md:text-lg text-charcoal/60 max-w-2xl mx-auto leading-relaxed mb-12 font-light">
            Retreaty jogi na Bali i Sri Lance z Julią Jakubowicz. 
            Małe grupy, piękne miejsca, czas na siebie i praktykę jogi 
            w otoczeniu natury.
          </p>

          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto mb-12">
            <div className="text-center">
              <div className="text-2xl font-primary font-medium text-temple-gold mb-1">30+</div>
              <div className="text-sm text-charcoal/70 font-secondary">Retreatów</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-primary font-medium text-temple-gold mb-1">8</div>
              <div className="text-sm text-charcoal/70 font-secondary">Lat doświadczenia</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-primary font-medium text-temple-gold mb-1">8-12</div>
              <div className="text-sm text-charcoal/70 font-secondary">Osób w grupie</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-primary font-medium text-temple-gold mb-1">RYT</div>
              <div className="text-sm text-charcoal/70 font-secondary">500 certyfikat</div>
            </div>
          </div>

          {/* Professional CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="/program"
              className="inline-flex items-center gap-3 px-10 py-4 bg-soft-black text-pure-white font-secondary font-medium tracking-wide transition-all duration-400 hover:bg-charcoal hover:shadow-lg group"
            >
              <span>Zobacz Program</span>
              <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
            
            <Link
              href="/kontakt"
              className="inline-flex items-center gap-3 px-10 py-4 bg-transparent text-charcoal border border-stone-light font-secondary font-medium tracking-wide transition-all duration-400 hover:border-temple-gold hover:text-temple-gold"
            >
              Skontaktuj się
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* Professional Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex flex-col items-center text-charcoal/50">
          <span className="text-xs mb-2 font-secondary tracking-wider uppercase">Poznaj ofertę</span>
          <div className="w-px h-8 bg-charcoal/20 animate-pulse"></div>
          <svg className="w-4 h-4 mt-2 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div 
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-temple-gold rectangular opacity-60"
          style={{
            animation: 'float 3s ease-in-out infinite'
          }}
        />
        <div 
          className="absolute top-1/3 right-1/3 w-1 h-1 bg-sage-green rectangular opacity-50"
          style={{
            animation: 'float 4s ease-in-out infinite',
            animationDelay: '1s'
          }}
        />
        <div 
          className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-charcoal rectangular opacity-40"
          style={{
            animation: 'float 5s ease-in-out infinite',
            animationDelay: '2s'
          }}
        />
      </div>


    </section>
  );
};

export default ProfessionalHero;