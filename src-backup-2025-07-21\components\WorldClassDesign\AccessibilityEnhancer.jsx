'use client';

import React, { useEffect, useState } from 'react';

/**
 * ♿ ACCESSIBILITY ENHANCER - TOP 1% DESIGN FEATURE
 * Zaawansowane funkcje dostępności
 * Inspirowane przez GOV.UK, najlepsze accessibility standards
 */
const AccessibilityEnhancer = ({ 
  enabled = true,
  showControls = true,
  className = '',
  ...props 
}) => {
  const [settings, setSettings] = useState({
    fontSize: 100,
    contrast: 100,
    dyslexiaMode: false,
    focusMode: false,
    readingMode: false,
    colorBlindMode: false,
    reducedMotion: false,
  });

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    // Load saved settings
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }

    // Apply system preferences
    const mediaQueries = {
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)'),
      highContrast: window.matchMedia('(prefers-contrast: high)'),
    };

    Object.entries(mediaQueries).forEach(([key, mq]) => {
      if (mq.matches) {
        setSettings(prev => ({ ...prev, [key]: true }));
      }
    });
  }, [enabled]);

  useEffect(() => {
    if (!enabled) return;

    // Save settings
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));

    // Apply settings
    applySettings();
  }, [settings, enabled]);

  const applySettings = () => {
    const root = document.documentElement;
    const body = document.body;

    // Font size
    root.style.fontSize = `${settings.fontSize}%`;

    // Contrast
    if (settings.contrast !== 100) {
      body.style.filter = `contrast(${settings.contrast}%)`;
    } else {
      body.style.filter = '';
    }

    // Dyslexia mode
    if (settings.dyslexiaMode) {
      body.classList.add('dyslexia-mode');
    } else {
      body.classList.remove('dyslexia-mode');
    }

    // Focus mode
    if (settings.focusMode) {
      body.classList.add('focus-mode');
    } else {
      body.classList.remove('focus-mode');
    }

    // Reading mode
    if (settings.readingMode) {
      body.classList.add('reading-mode');
    } else {
      body.classList.remove('reading-mode');
    }

    // Color blind mode
    if (settings.colorBlindMode) {
      body.classList.add('color-blind-mode');
    } else {
      body.classList.remove('color-blind-mode');
    }

    // Reduced motion
    if (settings.reducedMotion) {
      body.classList.add('reduced-motion');
    } else {
      body.classList.remove('reduced-motion');
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const resetSettings = () => {
    const defaultSettings = {
      fontSize: 100,
      contrast: 100,
      dyslexiaMode: false,
      focusMode: false,
      readingMode: false,
      colorBlindMode: false,
      reducedMotion: false,
    };
    setSettings(defaultSettings);
  };

  if (!enabled || !showControls) return null;

  return (
    <>
      {/* Accessibility control button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-blue-600 text-white rectangular shadow-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
        aria-label="Otwórz ustawienia dostępności"
        title="Ustawienia dostępności"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
        </svg>
      </button>

      {/* Accessibility panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Ustawienia dostępności
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Zamknij"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                {/* Font size */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rozmiar tekstu: {settings.fontSize}%
                  </label>
                  <input
                    type="range"
                    min="75"
                    max="150"
                    value={settings.fontSize}
                    onChange={(e) => updateSetting('fontSize', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rectangular appearance-none cursor-pointer"
                  />
                </div>

                {/* Contrast */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kontrast: {settings.contrast}%
                  </label>
                  <input
                    type="range"
                    min="75"
                    max="200"
                    value={settings.contrast}
                    onChange={(e) => updateSetting('contrast', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rectangular appearance-none cursor-pointer"
                  />
                </div>

                {/* Toggle settings */}
                {[
                  { key: 'dyslexiaMode', label: 'Tryb dla dyslektyków', desc: 'Ułatwia czytanie' },
                  { key: 'focusMode', label: 'Tryb skupienia', desc: 'Ukrywa rozpraszające elementy' },
                  { key: 'readingMode', label: 'Tryb czytania', desc: 'Upraszcza układ strony' },
                  { key: 'colorBlindMode', label: 'Tryb dla daltonistów', desc: 'Dostosowuje kolory' },
                  { key: 'reducedMotion', label: 'Ograniczone animacje', desc: 'Wyłącza animacje' },
                ].map(({ key, label, desc }) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-700">
                        {label}
                      </label>
                      <p className="text-xs text-gray-500">{desc}</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings[key]}
                        onChange={(e) => updateSetting(key, e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rectangular peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rectangular after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600`} />
                    </label>
                  </div>
                ))}

                {/* Reset button */}
                <button
                  onClick={resetSettings}
                  className="w-full py-2 px-4 border border-gray-300 rectangular shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Przywróć domyślne
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* CSS for accessibility modes */}
      <style jsx global>{`
        .dyslexia-mode * {
          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
        }
        
        .focus-mode .hero-section,
        .focus-mode .background-decoration {
          opacity: 0.3 !important;
        }
        
        .reading-mode {
          line-height: 1.8 !important;
        }
        
        .reading-mode * {
          max-width: 65ch !important;
        }
        
        .color-blind-mode {
          filter: grayscale(0.3) !important;
        }
        
        .reduced-motion *,
        .reduced-motion *::before,
        .reduced-motion *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
        
        @media (prefers-reduced-motion: reduce) {
          .reduced-motion *,
          .reduced-motion *::before,
          .reduced-motion *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        }
      `}</style>
    </>
  );
};

export default React.memo(AccessibilityEnhancer);