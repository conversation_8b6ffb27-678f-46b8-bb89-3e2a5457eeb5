/**
 * 🏗️ BAKASANA - STRUCTURED DATA MANAGER
 * 
 * Comprehensive Schema.org structured data implementation:
 * - Organization & LocalBusiness
 * - Events & Courses
 * - Reviews & Ratings
 * - FAQ & Articles
 * - Breadcrumbs & Navigation
 */

import { SEO_CONFIG } from './seoManager';

/**
 * Generate Organization schema
 */
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'TravelAgency',
    '@id': `${SEO_CONFIG.siteUrl}/#organization`,
    name: SEO_CONFIG.organization.name,
    alternateName: 'Retreaty Jogi Bali Sri Lanka',
    description: 'Najlepsze retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz. Transformacyjne podróże łączące praktykę jogi z odkrywaniem duchowej Azji.',
    url: SEO_CONFIG.organization.url,
    logo: {
      '@type': 'ImageObject',
      url: SEO_CONFIG.organization.logo,
      width: 512,
      height: 512
    },
    image: SEO_CONFIG.defaultImage,
    slogan: 'G<PERSON><PERSON> sens spotyka swój rytm',
    foundingDate: '2020',
    founder: {
      '@type': 'Person',
      name: '<PERSON> Jakubowicz',
      jobTitle: 'Instruktorka Jogi i Fizjoterapeutka',
      description: 'Certyfikowana instruktorka jogi 200h YTT, fizjoterapeutka, specjalistka od retreatów na Bali i Sri Lanka',
      image: `${SEO_CONFIG.siteUrl}/images/profile/julia-profile.jpg`,
      sameAs: Object.values(SEO_CONFIG.organization.socialMedia)
    },
    address: {
      '@type': 'PostalAddress',
      ...SEO_CONFIG.organization.address
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: SEO_CONFIG.organization.phone,
      email: SEO_CONFIG.organization.email,
      contactType: 'customer service',
      availableLanguage: ['Polish', 'English'],
      hoursAvailable: {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        opens: '08:00',
        closes: '22:00'
      }
    },
    sameAs: Object.values(SEO_CONFIG.organization.socialMedia),
    priceRange: '2900-4200 PLN',
    currenciesAccepted: 'PLN',
    paymentAccepted: ['Bank Transfer', 'BLIK', 'Credit Card'],
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '127',
      bestRating: '5',
      worstRating: '1'
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Retreaty Jogi 2025',
      itemListElement: [
        {
          '@type': 'Offer',
          name: 'Retreat Jogi Bali - Ubud & Gili Air',
          price: '3400',
          priceCurrency: 'PLN',
          availability: 'https://schema.org/InStock',
          url: `${SEO_CONFIG.siteUrl}/retreaty-jogi-bali-2025`,
          validFrom: '2025-01-01',
          validThrough: '2025-12-31'
        },
        {
          '@type': 'Offer',
          name: 'Retreat Jogi Sri Lanka - Sigiriya & Kandy',
          price: '3800',
          priceCurrency: 'PLN',
          availability: 'https://schema.org/InStock',
          url: `${SEO_CONFIG.siteUrl}/joga-sri-lanka-retreat`,
          validFrom: '2025-01-01',
          validThrough: '2025-12-31'
        }
      ]
    }
  };
}

/**
 * Generate Event schema for retreats
 */
export function generateEventSchema(eventData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    '@id': `${SEO_CONFIG.siteUrl}/events/${eventData.id}`,
    name: eventData.name,
    description: eventData.description,
    startDate: eventData.startDate,
    endDate: eventData.endDate,
    eventStatus: 'https://schema.org/EventScheduled',
    eventAttendanceMode: 'https://schema.org/OfflineEventAttendanceMode',
    location: {
      '@type': 'Place',
      name: eventData.location.name,
      address: {
        '@type': 'PostalAddress',
        addressLocality: eventData.location.city,
        addressCountry: eventData.location.country
      },
      geo: eventData.location.coordinates && {
        '@type': 'GeoCoordinates',
        latitude: eventData.location.coordinates.lat,
        longitude: eventData.location.coordinates.lng
      }
    },
    image: eventData.image ? `${SEO_CONFIG.siteUrl}${eventData.image}` : SEO_CONFIG.defaultImage,
    organizer: {
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    performer: {
      '@type': 'Person',
      name: 'Julia Jakubowicz',
      jobTitle: 'Instruktorka Jogi'
    },
    offers: {
      '@type': 'Offer',
      url: `${SEO_CONFIG.siteUrl}${eventData.url}`,
      price: eventData.price,
      priceCurrency: 'PLN',
      availability: 'https://schema.org/InStock',
      validFrom: eventData.bookingOpenDate,
      category: 'Yoga Retreat'
    },
    maximumAttendeeCapacity: eventData.maxParticipants || 12,
    remainingAttendeeCapacity: eventData.availableSpots || 8
  };
}

/**
 * Generate Course schema for online classes
 */
export function generateCourseSchema(courseData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: courseData.name,
    description: courseData.description,
    provider: {
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    instructor: {
      '@type': 'Person',
      name: 'Julia Jakubowicz',
      jobTitle: 'Instruktorka Jogi',
      description: 'Certyfikowana instruktorka jogi 200h YTT'
    },
    courseMode: courseData.mode || 'online',
    educationalLevel: courseData.level || 'Beginner',
    teaches: courseData.skills || ['Hatha Yoga', 'Vinyasa Yoga', 'Meditation'],
    timeRequired: courseData.duration,
    offers: {
      '@type': 'Offer',
      price: courseData.price,
      priceCurrency: 'PLN',
      availability: 'https://schema.org/InStock'
    }
  };
}

/**
 * Generate Review schema
 */
export function generateReviewSchema(reviewData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Review',
    itemReviewed: {
      '@type': 'Organization',
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    reviewRating: {
      '@type': 'Rating',
      ratingValue: reviewData.rating,
      bestRating: '5',
      worstRating: '1'
    },
    author: {
      '@type': 'Person',
      name: reviewData.author
    },
    reviewBody: reviewData.text,
    datePublished: reviewData.date
  };
}

/**
 * Generate FAQ schema
 */
export function generateFAQSchema(faqData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqData.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

/**
 * Generate Article schema for blog posts
 */
export function generateArticleSchema(articleData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: articleData.title,
    description: articleData.description,
    image: articleData.image ? `${SEO_CONFIG.siteUrl}${articleData.image}` : SEO_CONFIG.defaultImage,
    author: {
      '@type': 'Person',
      name: articleData.author || 'Julia Jakubowicz',
      url: `${SEO_CONFIG.siteUrl}/o-mnie`
    },
    publisher: {
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    datePublished: articleData.publishDate,
    dateModified: articleData.modifiedDate || articleData.publishDate,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${SEO_CONFIG.siteUrl}${articleData.url}`
    },
    articleSection: articleData.category,
    keywords: articleData.keywords,
    wordCount: articleData.wordCount
  };
}

/**
 * Generate Breadcrumb schema
 */
export function generateBreadcrumbSchema(breadcrumbs) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: `${SEO_CONFIG.siteUrl}${crumb.url}`
    }))
  };
}

/**
 * Generate Product schema for retreat packages
 */
export function generateProductSchema(productData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: productData.name,
    description: productData.description,
    image: productData.image ? `${SEO_CONFIG.siteUrl}${productData.image}` : SEO_CONFIG.defaultImage,
    brand: {
      '@type': 'Brand',
      name: SEO_CONFIG.organization.name
    },
    manufacturer: {
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    category: 'Yoga Retreat',
    offers: {
      '@type': 'Offer',
      url: `${SEO_CONFIG.siteUrl}${productData.url}`,
      priceCurrency: 'PLN',
      price: productData.price,
      availability: 'https://schema.org/InStock',
      seller: {
        '@id': `${SEO_CONFIG.siteUrl}/#organization`
      }
    },
    aggregateRating: productData.rating && {
      '@type': 'AggregateRating',
      ratingValue: productData.rating.value,
      reviewCount: productData.rating.count,
      bestRating: '5',
      worstRating: '1'
    }
  };
}

/**
 * Generate WebPage schema
 */
export function generateWebPageSchema(pageData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    '@id': `${SEO_CONFIG.siteUrl}${pageData.url}`,
    url: `${SEO_CONFIG.siteUrl}${pageData.url}`,
    name: pageData.title,
    description: pageData.description,
    isPartOf: {
      '@id': `${SEO_CONFIG.siteUrl}/#website`
    },
    about: {
      '@id': `${SEO_CONFIG.siteUrl}/#organization`
    },
    datePublished: pageData.publishDate,
    dateModified: pageData.modifiedDate,
    inLanguage: SEO_CONFIG.language,
    potentialAction: {
      '@type': 'ReadAction',
      target: `${SEO_CONFIG.siteUrl}${pageData.url}`
    }
  };
}

/**
 * Default FAQ data for BAKASANA
 */
export const DEFAULT_FAQ_DATA = [
  {
    question: 'Ile kosztuje retreat jogi na Bali?',
    answer: 'Retreat jogi na Bali z BAKASANA kosztuje od 3400 PLN za 7-dniowy program. Cena obejmuje zakwaterowanie, wszystkie posiłki, daily joga, medytację, ayurveda masaże i zwiedzanie. Dostępne są różne pakiety od 7 do 14 dni.'
  },
  {
    question: 'Czy potrzebuję doświadczenia w jodze?',
    answer: 'Nie, nasze retreaty są dostosowane do wszystkich poziomów zaawansowania. Julia Jakubowicz, certyfikowana instruktorka (200h YTT), prowadzi zajęcia dla początkujących i zaawansowanych. Każdy znajdzie coś dla siebie.'
  },
  {
    question: 'Kiedy najlepiej jechać na retreat na Bali?',
    answer: 'Najlepsze miesiące to kwiecień-październik (sucha pora) i grudzień-marzec. Unikamy września-listopada z powodu deszczów. Nasze retreaty odbywają się przez cały rok z dostosowaniem do warunków pogodowych.'
  },
  {
    question: 'Co jest zawarte w cenie retreatu?',
    answer: 'Cena obejmuje: zakwaterowanie w luksusowych hotelach 4-5*, wszystkie posiłki (vegetarian), daily joga i medytację, ayurveda masaże, zwiedzanie świątyń, transport lokalny, opiekę instruktora i tłumacza.'
  },
  {
    question: 'Jak małe są grupy na retreatach?',
    answer: 'Maksymalnie 12 osób w grupie, zwykle 8-10 uczestników. Zapewnia to indywidualne podejście, intymną atmosferę i możliwość bliższego poznania każdego uczestnika. Jakość przed ilością!'
  }
];
