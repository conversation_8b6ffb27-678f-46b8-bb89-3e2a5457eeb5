'use client';

import React from 'react';

/**
 * 🎨 NOISE TEXTURE - TOP 1% DESIGN FEATURE
 * Subtelny szum tekstury dla "invisible luxury"
 * Inspirowane przez Apple, Kinfolk, najlepsze editorial design
 */
const NoiseTexture = ({ 
  opacity = 0.015,
  frequency = 0.9,
  scale = 256,
  className = '',
  ...props 
}) => {
  // Generate SVG noise filter
  const noiseFilter = `
    <svg viewBox='0 0 ${scale} ${scale}' xmlns='http://www.w3.org/2000/svg'>
      <filter id='noiseFilter'>
        <feTurbulence 
          type='fractalNoise' 
          baseFrequency='${frequency}' 
          numOctaves='1' 
          stitchTiles='stitch'
        />
      </filter>
      <rect width='100%' height='100%' filter='url(#noiseFilter)'/>
    </svg>
  `;

  const encodedFilter = encodeURIComponent(noiseFilter);
  
  return (
    <div
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{
        backgroundImage: `url("data:image/svg+xml,${encodedFilter}")`,
        backgroundSize: `${scale}px ${scale}px`,
        opacity: opacity,
        mixBlendMode: 'multiply',
        zIndex: 1,
      }}
      {...props}
    />
  );
};

export default React.memo(NoiseTexture);