/**
 * 🌊 BAKASANA - PARALLAX SECTION COMPONENT
 * 
 * Premium parallax scrolling with:
 * - Multiple layer support
 * - Performance optimized
 * - Accessibility compliant
 * - Mobile responsive
 * - Smooth animations
 * 
 * Usage:
 * <ParallaxSection 
 *   backgroundImage="/images/hero.jpg"
 *   speed={0.5}
 *   className="min-h-screen"
 * >
 *   <h1>Content here</h1>
 * </ParallaxSection>
 */

'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useReducedMotion } from '@/hooks/useAdvancedAnimations';
import { cn } from '@/lib/utils';

const ParallaxSection = ({
  children,
  backgroundImage,
  speed = 0.5,
  className = '',
  overlay = true,
  overlayColor = 'rgba(0, 0, 0, 0.3)',
  direction = 'up',
  offset = 0,
  disabled = false,
  ...props
}) => {
  const containerRef = useRef(null);
  const backgroundRef = useRef(null);
  const [scrollY, setScrollY] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const prefersReducedMotion = useReducedMotion();
  
  // Disable parallax if user prefers reduced motion
  const shouldDisableParallax = prefersReducedMotion || disabled;
  
  useEffect(() => {
    if (shouldDisableParallax) return;
    
    const container = containerRef.current;
    if (!container) return;
    
    // Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        threshold: 0,
        rootMargin: '10% 0px'
      }
    );
    
    observer.observe(container);
    
    return () => observer.disconnect();
  }, [shouldDisableParallax]);
  
  useEffect(() => {
    if (shouldDisableParallax || !isVisible) return;
    
    let rafId;
    let ticking = false;
    
    const updateParallax = () => {
      if (!containerRef.current || !backgroundRef.current) return;
      
      const container = containerRef.current;
      const background = backgroundRef.current;
      const rect = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Calculate scroll progress
      const elementTop = rect.top;
      const elementHeight = rect.height;
      const scrollProgress = (windowHeight - elementTop) / (windowHeight + elementHeight);
      
      // Calculate transform value
      const speedMultiplier = direction === 'down' ? -speed : speed;
      const translateY = (scrollProgress - 0.5) * 100 * speedMultiplier + offset;
      
      // Apply transform
      background.style.transform = `translate3d(0, ${translateY}px, 0)`;
      
      ticking = false;
    };
    
    const handleScroll = () => {
      if (!ticking) {
        rafId = requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };
    
    // Initial call
    updateParallax();
    
    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', updateParallax, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', updateParallax);
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [speed, direction, offset, isVisible, shouldDisableParallax]);
  
  return (
    <div
      ref={containerRef}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      {...props}
    >
      {/* Background layer */}
      {backgroundImage && (
        <div
          ref={backgroundRef}
          className="absolute inset-0 will-change-transform"
          style={{
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            transform: shouldDisableParallax ? 'none' : 'translate3d(0, 0, 0)',
            scale: shouldDisableParallax ? '1' : '1.1', // Prevent gaps during parallax
          }}
        />
      )}
      
      {/* Overlay */}
      {overlay && (
        <div
          className="absolute inset-0 z-10"
          style={{
            backgroundColor: overlayColor,
            background: `linear-gradient(
              135deg,
              ${overlayColor} 0%,
              rgba(0, 0, 0, 0.1) 50%,
              ${overlayColor} 100%
            )`
          }}
        />
      )}
      
      {/* Content */}
      <div className="relative z-20">
        {children}
      </div>
    </div>
  );
};

// Multi-layer parallax component
export const MultiLayerParallax = ({
  children,
  layers = [],
  className = '',
  ...props
}) => {
  const containerRef = useRef(null);
  const layerRefs = useRef([]);
  const [isVisible, setIsVisible] = useState(false);
  const prefersReducedMotion = useReducedMotion();
  
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const container = containerRef.current;
    if (!container) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0, rootMargin: '10% 0px' }
    );
    
    observer.observe(container);
    return () => observer.disconnect();
  }, [prefersReducedMotion]);
  
  useEffect(() => {
    if (prefersReducedMotion || !isVisible) return;
    
    let rafId;
    let ticking = false;
    
    const updateLayers = () => {
      if (!containerRef.current) return;
      
      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const scrollProgress = (windowHeight - rect.top) / (windowHeight + rect.height);
      
      layerRefs.current.forEach((layer, index) => {
        if (!layer || !layers[index]) return;
        
        const { speed = 0.5, direction = 'up' } = layers[index];
        const speedMultiplier = direction === 'down' ? -speed : speed;
        const translateY = (scrollProgress - 0.5) * 100 * speedMultiplier;
        
        layer.style.transform = `translate3d(0, ${translateY}px, 0)`;
      });
      
      ticking = false;
    };
    
    const handleScroll = () => {
      if (!ticking) {
        rafId = requestAnimationFrame(updateLayers);
        ticking = true;
      }
    };
    
    updateLayers();
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (rafId) cancelAnimationFrame(rafId);
    };
  }, [layers, isVisible, prefersReducedMotion]);
  
  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      {...props}
    >
      {/* Background layers */}
      {layers.map((layer, index) => (
        <div
          key={index}
          ref={(el) => (layerRefs.current[index] = el)}
          className="absolute inset-0 will-change-transform"
          style={{
            backgroundImage: layer.image ? `url(${layer.image})` : undefined,
            backgroundColor: layer.color,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            zIndex: layer.zIndex || index,
            opacity: layer.opacity || 1,
            transform: prefersReducedMotion ? 'none' : 'translate3d(0, 0, 0)',
            scale: prefersReducedMotion ? '1' : '1.1',
          }}
        />
      ))}
      
      {/* Content */}
      <div className="relative z-50">
        {children}
      </div>
    </div>
  );
};

// Parallax text component
export const ParallaxText = ({
  children,
  speed = 0.3,
  direction = 'up',
  className = '',
  ...props
}) => {
  const textRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const prefersReducedMotion = useReducedMotion();
  
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    const text = textRef.current;
    if (!text) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0.1, rootMargin: '20% 0px' }
    );
    
    observer.observe(text);
    return () => observer.disconnect();
  }, [prefersReducedMotion]);
  
  useEffect(() => {
    if (prefersReducedMotion || !isVisible) return;
    
    let rafId;
    let ticking = false;
    
    const updateText = () => {
      if (!textRef.current) return;
      
      const text = textRef.current;
      const rect = text.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const scrollProgress = (windowHeight - rect.top) / (windowHeight + rect.height);
      
      const speedMultiplier = direction === 'down' ? -speed : speed;
      const translateY = (scrollProgress - 0.5) * 50 * speedMultiplier;
      
      text.style.transform = `translate3d(0, ${translateY}px, 0)`;
      
      ticking = false;
    };
    
    const handleScroll = () => {
      if (!ticking) {
        rafId = requestAnimationFrame(updateText);
        ticking = true;
      }
    };
    
    updateText();
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (rafId) cancelAnimationFrame(rafId);
    };
  }, [speed, direction, isVisible, prefersReducedMotion]);
  
  return (
    <div
      ref={textRef}
      className={cn(
        'will-change-transform',
        className
      )}
      style={{
        transform: prefersReducedMotion ? 'none' : 'translate3d(0, 0, 0)'
      }}
      {...props}
    >
      {children}
    </div>
  );
};

export default ParallaxSection;