async function getProgramData(slug) {
    // Tu moż<PERSON>z dodać faktyczne pobieranie danych z API lub bazy
    const programs = {
      'yoga-retreat-march-2025': {
        title: 'Yoga Retreat Marzec 2025',
        description: '10-dniowy retreat jogiczny na Bali',
        image: 'march-retreat.jpg',
        dates: '1-10 marca 2025'
      },
      'meditation-week-april-2025': {
        title: 'Tydzień Medytacji Kwiecień 2025',
        description: 'Tygodniowy program medytacyjny w Ubud',
        image: 'april-meditation.jpg',
        dates: '15-22 kwietnia 2025'
      }
    };
  
    return programs[slug] || null;
  }
  
  export async function generateMetadata({ params }) {
    const program = await getProgramData(params.slug);
  
    if (!program) {
      return {
        title: 'Program nie znaleziony | Bali Yoga Journey',
        robots: {
          index: false,
          follow: false
        }
      };
    }
  
    return {
      metadataBase: new URL('https://twoja-strona.pl'),
      title: `${program.title} | Bali Yoga Journey`,
      description: program.description,
      keywords: [
        'yoga retreat bali',
        'medytacja bali',
        program.title.toLowerCase(),
        'wyjazd joga',
        'bali' + program.dates
      ],
      openGraph: {
        title: program.title,
        description: program.description,
        images: [
          {
            url: `https://twoja-domena.pl/images/${program.image}`,
            width: 1200,
            height: 630,
            alt: program.title,
          }
        ],
        type: 'article',
      },
      alternates: {
        canonical: `https://twoja-domena.pl/program/${params.slug}`
      },
      robots: {
        index: true,
        follow: true,
        'max-image-preview': 'large',
        'max-video-preview': -1,
        'max-snippet': -1,
      },
      script: [
        {
          type: 'application/ld+json',
          text: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Event',
            name: program.title,
            description: program.description,
            startDate: program.dates.split('-')[0],
            endDate: program.dates.split('-')[1],
            location: {
              '@type': 'Place',
              name: 'Bali',
              address: {
                '@type': 'PostalAddress',
                addressCountry: 'ID',
                addressRegion: 'Bali'
              }
            },
            organizer: {
              '@type': 'Organization',
              name: 'Bali Yoga Journey',
              url: 'https://twoja-domena.pl'
            },
            eventStatus: 'https://schema.org/EventScheduled',
            eventAttendanceMode: 'https://schema.org/OfflineEventAttendanceMode'
          })
        }
      ]
    };
  }