'use client';

import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedInput - Ujednolicony system formularzy BAKASANA
 * Elegancja Old Money + Ciepły minimalizm
 */

const inputVariants = {
  // DEFAULT - Standardowe pola using unified design tokens
  default: {
    base: "bg-sanctuary border border-stone-light text-charcoal",
    focus: "focus:border-enterprise-brown focus:shadow-focus focus:outline-none",
    error: "border-terra focus:border-terra focus:shadow-focus"
  },

  // MINIMAL - Tylko dolna linia
  minimal: {
    base: "bg-transparent border-0 border-b border-stone-light text-charcoal",
    focus: "focus:border-enterprise-brown focus:outline-none",
    error: "border-terra focus:border-terra"
  },

  // WARM - Ciepłe, organiczne pola
  warm: {
    base: "bg-linen border border-whisper text-charcoal",
    focus: "focus:bg-sanctuary focus:border-enterprise-brown focus:shadow-focus focus:outline-none",
    error: "border-terra focus:border-terra focus:shadow-focus"
  }
};

const sizeVariants = {
  sm: "px-3 py-2 text-sm",
  md: "px-4 py-3 text-base",
  lg: "px-6 py-4 text-lg"
};

// Input Component
export const UnifiedInput = forwardRef(({
  variant = 'default',
  size = 'md',
  error = false,
  className = '',
  ...props
}, ref) => {
  const variantStyles = inputVariants[variant];
  const sizeStyles = sizeVariants[size];
  
  return (
    <input
      ref={ref}
      className={cn(
        // Base styles using unified design tokens
        "w-full font-secondary font-light transition-all duration-normal",
        "placeholder:text-sage placeholder:font-light",
        "disabled:opacity-50 disabled:cursor-not-allowed",

        // Variant styles
        error ? variantStyles.error : variantStyles.base,
        variantStyles.focus,

        // Size styles
        sizeStyles,

        // Custom className
        className
      )}
      {...props}
    />
  );
});

UnifiedInput.displayName = 'UnifiedInput';

// Textarea Component
export const UnifiedTextarea = forwardRef(({
  variant = 'default',
  size = 'md',
  error = false,
  rows = 4,
  className = '',
  ...props
}, ref) => {
  const variantStyles = inputVariants[variant];
  const sizeStyles = sizeVariants[size];
  
  return (
    <textarea
      ref={ref}
      rows={rows}
      className={cn(
        // Base styles
        "w-full font-lato font-light transition-all duration-300 resize-vertical",
        "placeholder:text-sage placeholder:font-light",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        
        // Variant styles
        error ? variantStyles.error : variantStyles.base,
        variantStyles.focus,
        
        // Size styles
        sizeStyles,
        
        // Custom className
        className
      )}
      {...props}
    />
  );
});

UnifiedTextarea.displayName = 'UnifiedTextarea';

// Label Component
export function UnifiedLabel({ 
  children, 
  required = false,
  className = '',
  ...props 
}) {
  return (
    <label 
      className={cn(
        "block text-sm font-lato font-light text-charcoal mb-2",
        "tracking-wide",
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span className="text-terra ml-1" aria-label="wymagane">*</span>
      )}
    </label>
  );
}

// Error Message Component
export function InputError({ children, className = '', ...props }) {
  if (!children) return null;
  
  return (
    <p 
      className={cn(
        "text-sm text-terra font-lato font-light mt-1",
        className
      )}
      role="alert"
      {...props}
    >
      {children}
    </p>
  );
}

// Helper Text Component
export function InputHelper({ children, className = '', ...props }) {
  if (!children) return null;
  
  return (
    <p 
      className={cn(
        "text-xs text-sage font-lato font-light mt-1",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

// Field Group Component - łączy label, input i error
export function FieldGroup({ 
  label,
  required = false,
  error,
  helper,
  children,
  className = '',
  ...props 
}) {
  return (
    <div className={cn("space-y-1", className)} {...props}>
      {label && (
        <UnifiedLabel required={required}>
          {label}
        </UnifiedLabel>
      )}
      {children}
      <InputError>{error}</InputError>
      <InputHelper>{helper}</InputHelper>
    </div>
  );
}

// Select Component
export const UnifiedSelect = forwardRef(({
  variant = 'default',
  size = 'md',
  error = false,
  children,
  className = '',
  ...props
}, ref) => {
  const variantStyles = inputVariants[variant];
  const sizeStyles = sizeVariants[size];
  
  return (
    <select
      ref={ref}
      className={cn(
        // Base styles
        "w-full font-lato font-light transition-all duration-300",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        "appearance-none bg-no-repeat bg-right bg-[length:16px_16px] pr-10",
        "bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzZCNkI2QiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K')]",
        
        // Variant styles
        error ? variantStyles.error : variantStyles.base,
        variantStyles.focus,
        
        // Size styles
        sizeStyles,
        
        // Custom className
        className
      )}
      {...props}
    >
      {children}
    </select>
  );
});

UnifiedSelect.displayName = 'UnifiedSelect';

// Checkbox Component
export const UnifiedCheckbox = forwardRef(({
  label,
  error = false,
  className = '',
  ...props
}, ref) => {
  return (
    <label className={cn("flex items-center space-x-3 cursor-pointer", className)}>
      <input
        ref={ref}
        type="checkbox"
        className={cn(
          "w-4 h-4 text-enterprise-brown bg-sanctuary border border-stone-light/30",
          "focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-0",
          "transition-all duration-200",
          error && "border-terra focus:ring-terra/20"
        )}
        {...props}
      />
      {label && (
        <span className="text-sm font-lato font-light text-charcoal">
          {label}
        </span>
      )}
    </label>
  );
});

UnifiedCheckbox.displayName = 'UnifiedCheckbox';