#!/usr/bin/env node

/**
 * 🎨 BAKASANA - COLOR MIGRATION SCRIPT
 * 
 * Automatycznie migruje wszystkie hardcoded kolory do design tokens
 * Konsoliduje 4 różne systemy kolorów w jeden spójny system
 */

const fs = require('fs');
const path = require('path');

// 🎯 MAPA MIGRACJI KOLORÓW - Z hardcoded na design tokens
const colorMap = {
  // ===== GŁÓWNE KOLORY TŁEM =====
  '#FDFCF8': 'var(--sanctuary)',
  '#FDFBF7': 'var(--sanctuary)', // Unifikacja
  '#F9F7F3': 'var(--whisper)',
  '#F9F6F1': 'var(--whisper)', // Unifikacja
  '#FBF8F4': 'var(--linen)',
  '#F6F2E8': 'var(--linen)', // Unifikacja
  '#F7F4F0': 'var(--silk)',
  '#F4F0E8': 'var(--silk)', // Unifikacja
  
  // ===== KOLORY TEKSTU =====
  '#2A2724': 'var(--charcoal)',
  '#3A3633': 'var(--charcoal)', // Unifikacja
  '#3A3A3A': 'var(--charcoal)', // Unifikacja
  '#4A453F': 'var(--charcoal-light)',
  '#5A524D': 'var(--charcoal-light)', // Unifikacja
  '#8B8680': 'var(--sage)',
  '#A8A39E': 'var(--stone)',
  '#B5B0A8': 'var(--stone-light)',
  '#C4BFB8': 'var(--stone-light)', // Unifikacja
  
  // ===== KOLORY AKCENTÓW =====
  '#B8935C': 'var(--temple-gold)',
  '#8B7355': 'var(--enterprise-brown)',
  '#C9A575': 'var(--temple-gold)', // Unifikacja głównego złota
  '#D4AF37': 'var(--golden-amber)',
  '#D4A574': 'var(--terracotta)',
  
  // ===== KOLORY SPECJALNE =====
  '#FFFFFF': 'var(--pure-white)',
  '#1A1816': 'var(--soft-black)',
  '#F5F2ED': 'var(--cream)',
  '#F5D5C8': 'var(--warm-peach)',
  '#9CAF88': 'var(--soft-sage)',
  '#FFE5E5': 'var(--blush)',
  '#FF6B6B': 'var(--friendly-coral)',
  '#C7B8EA': 'var(--calm-lavender)',
  
  // ===== RGBA I INNE FORMATY =====
  'rgba(253, 252, 248, 0.95)': 'rgba(var(--sanctuary-rgb), 0.95)',
  'rgba(139, 115, 85, 0.9)': 'rgba(var(--enterprise-brown-rgb), 0.9)',
  'rgba(201, 165, 117, 0.3)': 'rgba(var(--temple-gold-rgb), 0.3)',
  
  // ===== GRADIENTS =====
  '#C19B68': 'var(--temple-gold)',
  '#D1A46E': 'var(--golden-amber)',
  '#BE9561': 'var(--enterprise-brown)',
  
  // ===== BŁĘDNE/PRZESTARZAŁE KOLORY =====
  '#5B5754': 'var(--charcoal-light)',
  '#3D3A37': 'var(--charcoal)',
  '#8B857F': 'var(--sage)',
  '#6B6560': 'var(--ash)',
  '#D4D0CC': 'var(--stone-light)',
  '#9B9592': 'var(--stone)',
  '#2C2928': 'var(--charcoal)',
  '#999': 'var(--stone)',
  '#f0f0f0': 'var(--whisper)',
  '#E8E8E8': 'var(--stone-light)',
  '#5A5A5A': 'var(--charcoal-light)',
  '#a0a0a0': 'var(--stone)',
  '#2a2a2a': 'var(--charcoal)',
  '#1a1a1a': 'var(--soft-black)',
  '#F5F5F5': 'var(--whisper)',
};

// 🎯 MAPA KOLORÓW CSS PROPERTIES
const cssPropertyMap = {
  'background-color': true,
  'color': true,
  'border-color': true,
  'border-left-color': true,
  'border-right-color': true,
  'border-top-color': true,
  'border-bottom-color': true,
  'box-shadow': true,
  'text-shadow': true,
  'background': true,
  'border': true,
  'border-left': true,
  'border-right': true,
  'border-top': true,
  'border-bottom': true,
};

// 📁 PLIKI DO POMINIĘCIA
const skipFiles = [
  'node_modules',
  '.git',
  '.next',
  'dist',
  'build',
  'color-audit.txt',
  'migrate-colors.js'
];

// 📊 STATYSTYKI MIGRACJI
let stats = {
  filesProcessed: 0,
  filesChanged: 0,
  colorsReplaced: 0,
  errors: []
};

/**
 * 🔍 Sprawdza czy plik powinien być przetworzony
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  const allowedExtensions = ['.jsx', '.js', '.css', '.tsx', '.ts'];
  
  if (!allowedExtensions.includes(ext)) return false;
  
  for (const skip of skipFiles) {
    if (filePath.includes(skip)) return false;
  }
  
  return true;
}

/**
 * 🎨 Migruje kolory w pojedynczym pliku
 */
function migrateColorsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let replacements = 0;
    
    // Migracja każdego koloru z mapy
    Object.entries(colorMap).forEach(([oldColor, newColor]) => {
      // Różne formaty dopasowania
      const patterns = [
        new RegExp(`(['"]?)${oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\1`, 'gi'),
        new RegExp(`:\\s*${oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi'),
        new RegExp(`=\\s*['"]${oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'gi'),
      ];
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          content = content.replace(pattern, (match) => {
            replacements++;
            // Zachowaj format (cudzysłowy, dwukropek, etc.)
            if (match.includes(':')) {
              return match.replace(oldColor, newColor);
            } else if (match.includes('=')) {
              return match.replace(oldColor, newColor);
            } else {
              return match.replace(oldColor, newColor);
            }
          });
        }
      });
    });
    
    // Zapisz plik tylko jeśli były zmiany
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      stats.filesChanged++;
      stats.colorsReplaced += replacements;
      console.log(`✅ ${filePath}: ${replacements} kolorów zmigrowanych`);
    }
    
    stats.filesProcessed++;
    
  } catch (error) {
    stats.errors.push(`❌ ${filePath}: ${error.message}`);
    console.error(`❌ Błąd w ${filePath}:`, error.message);
  }
}

/**
 * 🗂️ Rekurencyjnie przetwarza katalog
 */
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Pomiń katalogi z listy skipFiles
        if (!skipFiles.some(skip => fullPath.includes(skip))) {
          processDirectory(fullPath);
        }
      } else if (shouldProcessFile(fullPath)) {
        migrateColorsInFile(fullPath);
      }
    });
    
  } catch (error) {
    console.error(`❌ Błąd przetwarzania katalogu ${dirPath}:`, error.message);
  }
}

/**
 * 🚀 GŁÓWNA FUNKCJA MIGRACJI
 */
function main() {
  console.log('🎨 BAKASANA - MIGRACJA KOLORÓW');
  console.log('=====================================');
  console.log('Konsolidacja 4 systemów kolorów w jeden...\n');
  
  // Utwórz backup przed migracją
  const backupDir = `src-backup-${new Date().toISOString().slice(0, 10)}`;
  if (!fs.existsSync(backupDir)) {
    console.log(`📦 Tworzenie backupu: ${backupDir}`);
    fs.cpSync('src', backupDir, { recursive: true });
  }
  
  // Rozpocznij migrację
  const startTime = Date.now();
  processDirectory('./src');
  
  // Wyświetl statystyki
  const duration = ((Date.now() - startTime) / 1000).toFixed(2);
  
  console.log('\n🎯 PODSUMOWANIE MIGRACJI');
  console.log('========================');
  console.log(`⏱️  Czas wykonania: ${duration}s`);
  console.log(`📁 Plików przetworzonych: ${stats.filesProcessed}`);
  console.log(`✅ Plików zmienionych: ${stats.filesChanged}`);
  console.log(`🎨 Kolorów zmigrowanych: ${stats.colorsReplaced}`);
  
  if (stats.errors.length > 0) {
    console.log(`\n❌ BŁĘDY (${stats.errors.length}):`);
    stats.errors.forEach(error => console.log(error));
  }
  
  console.log('\n🎉 MIGRACJA ZAKOŃCZONA!');
  console.log('Następne kroki:');
  console.log('1. Sprawdź zmiany: git diff');
  console.log('2. Przetestuj aplikację: npm run dev');
  console.log('3. Usuń niepotrzebne pliki CSS');
  console.log('4. Zatwierdź zmiany: git commit');
}

// Uruchom migrację
if (require.main === module) {
  main();
}

module.exports = { migrateColorsInFile, colorMap };
