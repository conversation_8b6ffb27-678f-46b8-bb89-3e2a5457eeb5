'use client';

import React, { useState, useEffect, useRef } from 'react';

const SpectacularHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const heroRef = useRef(null);

  // Simple scroll tracking
  const [titleOpacity, setTitleOpacity] = useState(1);
  const [titleY, setTitleY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);

    // Scroll listener for subtle parallax
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setTitleOpacity(Math.max(0, 1 - scrolled / 400));
      setTitleY(-scrolled / 6);
    };

    window.addEventListener('scroll', handleScroll);

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Removed fireflies animation for clean old money aesthetic

  // Simple fade-in animation for old money elegance
  const getTitleStyle = () => ({
    opacity: isLoaded ? 1 : 0,
    transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
    transition: 'all 2s ease-out 0.5s'
  });

  const title = "BAKASANA";

  return (
    <section
      ref={heroRef}
      className="hero hero-breathing"
    >
      {/* Background - Gradient Świtu zamiast obrazu */}
      <div className="absolute inset-0 z-0">
        {/* Gradient świtu - od pearl white górą do warm sand dołem */}
        <div 
          className="absolute inset-0"
          style={{
            background: 'var(--gradient-hero)'
          }}
        />
        
        {/* Subtelna tekstura ziarno papieru */}
        <div
          className="absolute inset-0 opacity-15"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='paperTexture'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.04' numOctaves='5' result='noise' seed='1'/%3E%3CfeColorMatrix in='noise' type='saturate' values='0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23paperTexture)' opacity='0.4'/%3E%3C/svg%3E")`,
            mixBlendMode: 'overlay'
          }}
        />
        
        {/* Rozmyte sylwetki kobiet w ruchu - ledwo widoczne */}
        <div className="absolute inset-0 opacity-5">
          <div 
            className="absolute top-1/4 left-1/4 w-32 h-48 rounded-full"
            style={{
              background: 'radial-gradient(ellipse, rgba(139, 115, 85, 0.3) 0%, transparent 70%)',
              filter: 'blur(40px)',
              transform: 'rotate(-15deg)'
            }}
          />
          <div 
            className="absolute top-1/3 right-1/3 w-28 h-44 rounded-full"
            style={{
              background: 'radial-gradient(ellipse, rgba(232, 180, 184, 0.2) 0%, transparent 70%)',
              filter: 'blur(35px)',
              transform: 'rotate(20deg)'
            }}
          />
          <div 
            className="absolute bottom-1/3 left-1/2 w-24 h-40 rounded-full"
            style={{
              background: 'radial-gradient(ellipse, rgba(139, 115, 85, 0.2) 0%, transparent 70%)',
              filter: 'blur(45px)',
              transform: 'rotate(-10deg)'
            }}
          />
        </div>
      </div>





      {/* Main Content */}
      <div
        className="hero-content-optimized"
        style={{
          opacity: titleOpacity,
          transform: `translateY(${titleY}px)`
        }}
      >
        {/* Main Title - Playfair Display with Breathing */}
        <div className="mb-6 relative">
          <h1
            className="font-playfair font-normal tracking-wide breathe-text"
            style={{
              ...getTitleStyle(),
              fontSize: 'clamp(60px, 10vw, 120px)',
              color: '#3A3633', // SOFT CHARCOAL zamiast białego
              textShadow: '0 2px 8px rgba(0,0,0,0.03)',
              lineHeight: '1.1' // Slightly more breathing room
            }}
          >
            {title}
          </h1>
        </div>

        {/* Osobisty akcent kursywą z warm underline */}
        <p
          className="text-handwriting breathe-gentle"
          style={{
            fontSize: '24px',
            opacity: isLoaded ? 0.9 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'all 1.8s ease 1.2s',
            marginBottom: '40px',
            letterSpacing: '0.02em'
          }}
        >
          gdzie każda z nas <span className="warm-underline active">odnajduje swoją magię</span>
        </p>

        {/* Enhanced subtitle with breathing */}
        <p
          className="body-breathing breathe-gentle"
          style={{
            fontSize: '16px',
            color: '#8B8680', // Delikatny kolor zamiast białego
            opacity: isLoaded ? 0.8 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'all 1.5s ease 1.5s',
            marginTop: '20px',
            letterSpacing: '0.05em'
          }}
        >
          joga to powrót do domu
        </p>
      </div>


    </section>
  );
};

export default SpectacularHero;
