'use client';

import Image from 'next/image';
import { useResponsiveStyles, getHeroStyles, getSectionStyles } from '../../components/OnlineClassesStyles';

export default function ZajeciaOnlinePage() {
  const { isDesktop, isTablet, isMobile } = useResponsiveStyles();
  const heroStyles = getHeroStyles(isDesktop, isTablet, isMobile);
  const sectionStyles = getSectionStyles(isDesktop, isTablet, isMobile);
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Zajęcia Jogi Online - Julia Jakubowicz",
    "description": "Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",
    "provider": {
      "@type": "Person",
      "name": "<PERSON>"
    }
  };

  // Dane sekc<PERSON> zgodnie z opisem wizualnym
  const privateClasses = {
    title: "Praktyka tylko dla Ciebie",
    subtitle: "Twoja osobista przestrzeń do odkrywania jogi w zaciszu własnego domu - jak spotkanie z najlepszą przyjaciółką.",
    description: "Każda sesja jest stworzona specjalnie dla Ciebie - dostosowuję wszystko do Twojego poziomu, potrzeb ciała i tego, co dzieje się w Twoim sercu. Tworzymy razem bezpieczną przestrzeń, gdzie możesz być sobą i odkrywać magię jogi - od asany po oddech i medytację.",
    features: [
      "Poznajemy się - rozmawiamy o Twoich marzeniach i celach",
      "Tworzę program tylko dla Ciebie - jak szyta na miarę sukienka",
      "Delikatnie prowadzę i koryguje w czasie rzeczywistym",
      "Dostajesz nagranie, żeby praktykować kiedy chcesz",
      "Jestem z Tobą między sesjami - pytaj o wszystko!"
    ],
    offerings: [
      { name: "Poznajmy się", duration: "30 min", note: "Darmowa rozmowa o Twoich marzeniach" },
      { name: "Sesja dla Ciebie", duration: "60 min", note: "Pełna praktyka szyta na miarę" },
      { name: "Miesięczna podróż", duration: "4 x 60 min", note: "Pogłębiamy praktykę razem" },
      { name: "Transformacja", duration: "8 x 60 min", note: "Prawdziwa przemiana ciała i ducha" }
    ]
  };

  const groupClasses = {
    title: "Praktyka z przyjaciółkami",
    subtitle: "Energia wspólnoty w małych, intymnych grupach - jak spotkanie z najlepszymi koleżankami.",
    description: "Praktykujesz z innymi kobietami, czerpiesz siłę z naszej wspólnej energii, a jednocześnie widzę każdą z was i daję personalną uwagę. To nie są anonimowe zajęcia - to nasza mała wspólnota, gdzie każda jest ważna.",
    schedule: [
      { day: "Poniedziałek", time: "18:00", type: "Hatha Yoga", level: "delikatny start tygodnia" },
      { day: "Środa", time: "19:00", type: "Vinyasa Flow", level: "energia w środku tygodnia" },
      { day: "Piątek", time: "17:30", type: "Yin Yoga", level: "relaks na weekend" },
      { day: "Sobota", time: "10:00", type: "Yoga Terapeutyczna", level: "dbamy o ciało i duszę" }
    ],
    offerings: [
      { name: "Mała grupa", size: "4-6 kobiet", note: "Intymnie, jak u przyjaciółki" },
      { name: "Większa grupa", size: "7-12 kobiet", note: "Energia wspólnoty" },
      { name: "Miesięczna przygoda", size: "8 praktyk", note: "Budujemy nawyk razem" },
      { name: "Kwartalna transformacja", size: "24 praktyki", note: "Prawdziwa przemiana" }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <main className="bg-sanctuary min-h-screen">
        {/* HERO SECTION - Magazine Style Header */}
        <section className="magazine-hero">
          <div className="magazine-hero-content">
            <div className="magazine-header-line"></div>
            
            <h1 className="magazine-title">
              Online
            </h1>

            <p className="magazine-subtitle">
              Joga w Twoim domu - jak spotkanie z przyjaciółką
            </p>

            <div className="magazine-meta">
              Indywidualne sesje i grupy pełne ciepła
            </div>
            
            <div className="magazine-header-line"></div>
          </div>
        </section>

        {/* SEKCJA INDYWIDUALNE - Dwukolumnowa */}
        <section style={sectionStyles.section}>
          <div style={sectionStyles.grid}>
            {/* Kolumna tekstowa (lewa) */}
            <div>
              <h2 style={sectionStyles.sectionTitle}>
                {privateClasses.title}
              </h2>

              <p style={{
                fontSize: '17px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: 'var(--charcoal-light)',
                lineHeight: '1.8',
                marginBottom: '32px'
              }}>
                {privateClasses.subtitle}
              </p>

              <div style={{
                fontSize: '15px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: 'var(--charcoal)',
                lineHeight: '1.9',
                letterSpacing: '0.02em',
                marginBottom: '56px',
                textAlign: 'left'
              }}>
                {privateClasses.description}
              </div>

              <div style={{marginBottom: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: 'var(--charcoal)',
                  marginBottom: '28px'
                }}>
                  Jak wyglądają nasze spotkania
                </h3>
                
                <div className="space-y-sm">
                  {privateClasses.features.map((feature, index) => (
                    <div key={index} className="py-sm pl-lg border-l border-enterprise-brown/10 relative">
                      <span className="text-caption font-secondary font-light text-ash">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div style={{marginTop: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: 'var(--charcoal)',
                  marginBottom: '28px'
                }}>
                  Wybierz swoją ścieżkę
                </h3>
                
                <div>
                  {privateClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '20px 0',
                      borderBottom: index === privateClasses.offerings.length - 1 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                    }}>
                      <div>
                        <h4 style={{
                          fontSize: '15px',
                          fontFamily: 'Inter',
                          fontWeight: 400,
                          color: 'var(--charcoal)',
                          margin: '0 0 4px 0'
                        }}>
                          {option.name}
                        </h4>
                        <p style={{
                          fontSize: '13px',
                          fontFamily: 'Inter',
                          fontWeight: 300,
                          color: 'var(--sage)',
                          margin: 0
                        }}>
                          {option.duration}
                        </p>
                      </div>
                      <span style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: 'var(--sage)'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Kolumna wizualna (prawa) */}
            <div style={sectionStyles.imageContainer}>
              <Image
                src="/images/profile/omnie-opt.webp"
                alt="Indywidualne sesje jogi online"
                fill
                style={{
                  objectFit: 'cover',
                  filter: 'grayscale(100%) brightness(1.05)'
                }}
                sizes={isMobile ? '100vw' : '45vw'}
                quality={95}
              />
            </div>
          </div>
        </section>

        {/* LINIA PODZIAŁU */}
        <div style={sectionStyles.divider}></div>

        {/* SEKCJA GRUPOWE - Odwrócona dwukolumnowa */}
        <section style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: isMobile ? '0 40px' : isTablet ? '0 60px' : '0 80px'
        }}>
          <div style={sectionStyles.gridReverse}>
            {/* Kolumna wizualna (lewa) */}
            <div style={sectionStyles.visualElement}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: isMobile ? '10px' : '20px'
              }}>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(5px)' : 'translateX(10px)'
                }}></div>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(-5px)' : 'translateX(-10px)'
                }}></div>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(-15px)' : 'translateX(-30px)'
                }}></div>
              </div>
            </div>

            {/* Kolumna tekstowa (prawa) */}
            <div>
              <h2 style={sectionStyles.sectionTitle}>
                {groupClasses.title}
              </h2>

              <p style={{
                fontSize: '17px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: 'var(--charcoal-light)',
                lineHeight: '1.8',
                marginBottom: '32px'
              }}>
                {groupClasses.subtitle}
              </p>

              <div style={{
                fontSize: '15px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: 'var(--charcoal)',
                lineHeight: '1.9',
                letterSpacing: '0.02em',
                marginBottom: '56px',
                textAlign: 'left'
              }}>
                {groupClasses.description}
              </div>

              <div style={{marginBottom: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: 'var(--charcoal)',
                  marginBottom: '28px'
                }}>
                  Kiedy się spotykamy
                </h3>
                
                <div>
                  {groupClasses.schedule.map((session, index) => (
                    <div key={index} style={{
                      padding: '18px 0',
                      fontSize: '14px',
                      fontFamily: 'Inter',
                      color: 'var(--charcoal)'
                    }}>
                      <span style={{fontWeight: 400}}>{session.day}</span>
                      <span style={{color: 'var(--stone-light)', margin: '0 8px'}}>|</span>
                      <span style={{fontWeight: 300}}>{session.time}</span>
                      <span style={{color: 'var(--stone-light)', margin: '0 8px'}}>|</span>
                      <span style={{fontWeight: 300, color: 'var(--ash)'}}>{session.type}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div style={{marginTop: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: 'var(--charcoal)',
                  marginBottom: '28px'
                }}>
                  Nasze opcje
                </h3>
                
                <div>
                  {groupClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '20px 0',
                      borderBottom: index === groupClasses.offerings.length - 1 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                    }}>
                      <div>
                        <h4 style={{
                          fontSize: '15px',
                          fontFamily: 'Inter',
                          fontWeight: 400,
                          color: 'var(--charcoal)',
                          margin: '0 0 4px 0'
                        }}>
                          {option.name}
                        </h4>
                        <p style={{
                          fontSize: '13px',
                          fontFamily: 'Inter',
                          fontWeight: 300,
                          color: 'var(--sage)',
                          margin: 0
                        }}>
                          {option.size}
                        </p>
                      </div>
                      <span style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: 'var(--sage)'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* SEKCJA CTA */}
        <section style={sectionStyles.ctaSection}>
          <h2 style={sectionStyles.ctaTitle}>
            Rozpocznij swoją praktykę
          </h2>

          <p style={{
            fontSize: '16px',
            fontFamily: 'Inter',
            fontWeight: 300,
            color: 'var(--charcoal-light)',
            lineHeight: '1.7',
            marginBottom: '48px'
          }}>
            Skontaktuj się ze mną, aby omówić szczegóły i znaleźć formę praktyki, która będzie odpowiadać Twoim potrzebom.
          </p>

          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '16px'
          }}>
            <button style={{
              padding: '16px 48px',
              border: '1px solid rgba(61, 58, 55, 0.3)',
              backgroundColor: 'transparent',
              fontSize: '12px',
              fontFamily: 'Inter',
              fontWeight: 400,
              letterSpacing: '0.15em',
              textTransform: 'uppercase',
              color: 'var(--charcoal)',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = 'var(--charcoal)';
              e.target.style.color = '#FDFAF7';
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = 'var(--charcoal)';
            }}
            onClick={() => window.location.href = '/kontakt'}
            >
              UMÓW SESJĘ
            </button>
            
            <p style={{
              fontSize: '13px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: 'var(--sage)',
              margin: 0
            }}>
              Pierwsza konsultacja 30 min bez opłat
            </p>
          </div>
        </section>
      </main>
    </>
  );
}
