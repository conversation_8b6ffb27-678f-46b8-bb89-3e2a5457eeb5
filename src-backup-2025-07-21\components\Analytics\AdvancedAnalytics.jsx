'use client';

import { useEffect } from 'react';
import Script from 'next/script';
import { usePathname } from 'next/navigation';

const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID;
const GA4_ID = process.env.NEXT_PUBLIC_GA4_ID;
const HOTJAR_ID = process.env.NEXT_PUBLIC_HOTJAR_ID;
const CLARITY_ID = process.env.NEXT_PUBLIC_CLARITY_ID;

export default function AdvancedAnalytics() {
  const pathname = usePathname();

  // Page view tracking
  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA4_ID, {
        page_path: pathname,
      });
    }
  }, [pathname]);

  // Enhanced ecommerce tracking
  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      // Track retreat booking funnel
      window.gtag('event', 'page_view', {
        event_category: 'Navigation',
        event_label: pathname,
        custom_map: {
          dimension1: 'Retreat_Booking_Flow',
          dimension2: pathname
        }
      });
    }
  }, [pathname]);

  return (
    <>
      {/* Google Tag Manager */}
      {GTM_ID && (
        <>
          <Script
            id="gtm-script"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${GTM_ID}');
              `,
            }}
          />
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            />
          </noscript>
        </>
      )}

      {/* Enhanced GA4 */}
      {GA4_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`}
            strategy="afterInteractive"
          />
          <Script
            id="ga4-config"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                
                gtag('config', '${GA4_ID}', {
                  page_path: window.location.pathname,
                  custom_map: {
                    'custom_parameter_1': 'retreat_type',
                    'custom_parameter_2': 'user_journey_stage'
                  }
                });

                // Enhanced ecommerce for retreat bookings
                gtag('config', '${GA4_ID}', {
                  send_page_view: false,
                  custom_map: {
                    'custom_parameter_1': 'retreat_destination',
                    'custom_parameter_2': 'retreat_price_range'
                  }
                });

                // Track scroll depth
                let scrollDepth = 0;
                window.addEventListener('scroll', () => {
                  const depth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
                  if (depth > scrollDepth && depth % 25 === 0) {
                    scrollDepth = depth;
                    gtag('event', 'scroll_depth', {
                      event_category: 'Engagement',
                      event_label: depth + '%',
                      value: depth
                    });
                  }
                });
              `,
            }}
          />
        </>
      )}

      {/* Hotjar - Heatmaps & User Recordings */}
      {HOTJAR_ID && (
        <Script
          id="hotjar-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
      )}

      {/* Microsoft Clarity */}
      {CLARITY_ID && (
        <Script
          id="clarity-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${CLARITY_ID}");
            `,
          }}
        />
      )}

      {/* Facebook Pixel */}
      <Script
        id="facebook-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID}');
            fbq('track', 'PageView');
          `,
        }}
      />

      {/* Custom Event Tracking */}
      <Script
        id="custom-events"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Track retreat interest
            window.trackRetreatInterest = function(retreatType, destination) {
              gtag('event', 'retreat_interest', {
                event_category: 'Retreat',
                event_label: retreatType,
                custom_parameter_1: destination,
                value: 1
              });
            };

            // Track booking funnel
            window.trackBookingStep = function(step, retreatId) {
              gtag('event', 'booking_step', {
                event_category: 'Booking',
                event_label: step,
                custom_parameter_1: retreatId,
                value: step === 'complete' ? 1 : 0
              });
            };

            // Track yoga video engagement
            window.trackYogaVideo = function(action, videoId) {
              gtag('event', 'yoga_video_' + action, {
                event_category: 'Video',
                event_label: videoId,
                value: action === 'complete' ? 1 : 0
              });
            };

            // Track newsletter signup
            window.trackNewsletterSignup = function(location) {
              gtag('event', 'newsletter_signup', {
                event_category: 'Lead',
                event_label: location,
                value: 1
              });
            };
          `,
        }}
      />
    </>
  );
}