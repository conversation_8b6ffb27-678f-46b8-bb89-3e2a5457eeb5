'use client';

import React, { useState, useEffect } from 'react';

/**
 * ♿ PROGRESSIVE ENHANCEMENT UTILITIES
 * 
 * Ensures core functionality works without JavaScript
 * and provides graceful fallbacks for enhanced features
 */

// Hook to detect if JavaScript is enabled
export const useJavaScriptEnabled = () => {
  const [jsEnabled, setJsEnabled] = useState(false);
  
  useEffect(() => {
    setJsEnabled(true);
  }, []);
  
  return jsEnabled;
};

// Component that only renders when JavaScript is enabled
export const JavaScriptOnly = ({ children, fallback = null }) => {
  const jsEnabled = useJavaScriptEnabled();
  
  if (!jsEnabled) {
    return fallback;
  }
  
  return children;
};

// Component that only renders when JavaScript is disabled
export const NoJavaScript = ({ children }) => {
  const jsEnabled = useJavaScriptEnabled();
  
  if (jsEnabled) {
    return null;
  }
  
  return children;
};

// Enhanced form that works without JavaScript
export const ProgressiveForm = ({ 
  action, 
  method = 'POST', 
  children, 
  onSubmit,
  className = '',
  ...props 
}) => {
  const jsEnabled = useJavaScriptEnabled();
  
  const handleSubmit = (e) => {
    if (jsEnabled && onSubmit) {
      e.preventDefault();
      onSubmit(e);
    }
    // If JS is disabled, form will submit normally to action URL
  };
  
  return (
    <form
      action={action}
      method={method}
      onSubmit={handleSubmit}
      className={className}
      {...props}
    >
      {children}
      
      {/* Hidden field to indicate JS status */}
      <input type="hidden" name="js_enabled" value={jsEnabled ? '1' : '0'} />
    </form>
  );
};

// Progressive navigation that works without JavaScript
export const ProgressiveNavigation = ({ items, className = '' }) => {
  const jsEnabled = useJavaScriptEnabled();
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <nav className={className} role="navigation">
      {/* Mobile menu toggle - only show if JS enabled */}
      {jsEnabled && (
        <button
          className="mobile-menu-toggle md:hidden"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
          aria-controls="mobile-menu"
          aria-label="Toggle navigation menu"
        >
          <span className="sr-only">Menu</span>
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      )}
      
      {/* Navigation items */}
      <ul 
        id="mobile-menu"
        className={`
          navigation-list
          ${jsEnabled ? (isOpen ? 'block' : 'hidden md:flex') : 'block'}
          md:flex md:space-x-6
        `}
      >
        {items.map((item, index) => (
          <li key={index} className="navigation-item">
            <a 
              href={item.href}
              className="navigation-link"
              aria-current={item.current ? 'page' : undefined}
            >
              {item.label}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
};

// Progressive modal that falls back to new page without JS
export const ProgressiveModal = ({ 
  isOpen, 
  onClose, 
  href, 
  children, 
  title,
  className = '' 
}) => {
  const jsEnabled = useJavaScriptEnabled();
  
  // Without JS, render as a link to the full page
  if (!jsEnabled) {
    return (
      <a 
        href={href} 
        className="progressive-modal-link"
        aria-label={`Open ${title} in new page`}
      >
        {title}
      </a>
    );
  }
  
  // With JS, render as modal
  if (!isOpen) return null;
  
  return (
    <div 
      className={`modal-overlay ${className}`}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h2 id="modal-title">{title}</h2>
          <button
            onClick={onClose}
            className="modal-close"
            aria-label="Close modal"
          >
            ×
          </button>
        </div>
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  );
};

// Progressive image gallery that works without JS
export const ProgressiveGallery = ({ images, className = '' }) => {
  const jsEnabled = useJavaScriptEnabled();
  const [selectedImage, setSelectedImage] = useState(null);
  
  if (!jsEnabled) {
    // Without JS, render as simple grid with links to full images
    return (
      <div className={`image-grid ${className}`}>
        {images.map((image, index) => (
          <div key={index} className="image-item">
            <a href={image.fullSize} target="_blank" rel="noopener noreferrer">
              <Image
                src={image.thumbnail}
                alt={image.alt}
                width={200}
                height={150}
                className="thumbnail"
                quality={85}
                sizes="(max-width: 768px) 100vw, 200px"
              />
              <span className="sr-only">View full size image: {image.alt}</span>
            </a>
          </div>
        ))}
      </div>
    );
  }
  
  // With JS, render interactive gallery
  return (
    <div className={`progressive-gallery ${className}`}>
      <div className="image-grid">
        {images.map((image, index) => (
          <button
            key={index}
            className="image-item"
            onClick={() => setSelectedImage(image)}
            aria-label={`View ${image.alt}`}
          >
            <img 
              src={image.thumbnail} 
              alt={image.alt}
              className="thumbnail"
            />
          </button>
        ))}
      </div>
      
      {selectedImage && (
        <div 
          className="lightbox"
          role="dialog"
          aria-modal="true"
          aria-labelledby="lightbox-title"
        >
          <div className="lightbox-content">
            <button
              onClick={() => setSelectedImage(null)}
              className="lightbox-close"
              aria-label="Close lightbox"
            >
              ×
            </button>
            <img 
              src={selectedImage.fullSize} 
              alt={selectedImage.alt}
              className="lightbox-image"
            />
            <p id="lightbox-title" className="lightbox-caption">
              {selectedImage.alt}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

// Progressive search that works without JS
export const ProgressiveSearch = ({ 
  action = '/search', 
  placeholder = 'Search...', 
  onSearch,
  className = '' 
}) => {
  const jsEnabled = useJavaScriptEnabled();
  const [query, setQuery] = useState('');
  
  const handleSubmit = (e) => {
    if (jsEnabled && onSearch) {
      e.preventDefault();
      onSearch(query);
    }
    // Without JS, form submits to action URL
  };
  
  return (
    <form 
      action={action}
      method="GET"
      onSubmit={handleSubmit}
      className={`progressive-search ${className}`}
      role="search"
    >
      <label htmlFor="search-input" className="sr-only">
        Search
      </label>
      <input
        id="search-input"
        type="search"
        name="q"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        className="search-input"
        required
      />
      <button 
        type="submit"
        className="search-button"
        aria-label="Submit search"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </button>
    </form>
  );
};

// Utility to add no-js class to body
export const NoJSDetector = () => {
  useEffect(() => {
    // Remove no-js class when JS loads
    document.body.classList.remove('no-js');
    document.body.classList.add('js');
  }, []);
  
  return null;
};

export default {
  useJavaScriptEnabled,
  JavaScriptOnly,
  NoJavaScript,
  ProgressiveForm,
  ProgressiveNavigation,
  ProgressiveModal,
  ProgressiveGallery,
  ProgressiveSearch,
  NoJSDetector
};
