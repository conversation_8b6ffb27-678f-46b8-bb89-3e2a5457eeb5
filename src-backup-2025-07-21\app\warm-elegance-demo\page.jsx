'use client';

import React from 'react';
import { 
  HeroTitle, 
  SectionTitle, 
  CardTitle, 
  BodyText, 
  HandwritingText, 
  Quote,
  WarmDivider,
  Divider
} from '@/components/ui/UnifiedTypography';
import UnifiedButton from '@/components/ui/UnifiedButton';
import UnifiedCard from '@/components/ui/UnifiedCard';

export default function WarmEleganceDemoPage() {
  return (
    <div className="min-h-screen bg-sanctuary">
      {/* Hero Section with Breathing Background */}
      <section className="hero-breathing py-32 px-8">
        <div className="max-w-4xl mx-auto text-center">
          <HeroTitle>
            BAKASANA
          </HeroTitle>
          <HandwritingText className="text-2xl block mb-8">
            gdzie każda kobieta <span className="warm-underline active">odnajduje swoją siłę</span>
          </HandwritingText>
          <BodyText className="text-lg max-w-2xl mx-auto" breathing>
            Transformacja z ekskluzywnego luksusu do ciepłej elegancji. 
            Odkryj nowy język wizualny BAKASANA - pełen ciepła, autentyczności i oddychającej przestrzeni.
          </BodyText>
        </div>
      </section>

      <WarmDivider />

      {/* Typography Showcase */}
      <section className="py-20 px-8">
        <div className="max-w-6xl mx-auto">
          <SectionTitle warmAccent="Playfair Display">
            Nowa Typografia z Playfair Display
          </SectionTitle>
          
          <div className="grid md:grid-cols-2 gap-12 mb-16">
            <div>
              <CardTitle breathing>
                Elegancka Typografia z Ciepłym Charakterem
              </CardTitle>
              <BodyText breathing>
                Główny font to teraz <strong className="warm-underline">Playfair Display</strong> - 
                serif z większą osobowością. Zachowujemy elegancję, ale dodajemy ciepło i ludzki charakter.
              </BodyText>
              <BodyText breathing>
                Zwiększona inter-linia daje więcej <HandwritingText>oddechu</HandwritingText> tekstowi, 
                a kluczowe słowa podkreślamy ciepłym kolorem zamiast pogrubienia.
              </BodyText>
            </div>
            
            <div className="space-y-6">
              <div>
                <h3 className="font-playfair text-2xl mb-3 text-charcoal">Przykład nagłówka</h3>
                <p className="body-breathing">
                  Praktyka jogi to <span className="warm-underline active">powrót do siebie</span>
                </p>
              </div>
              
              <div>
                <h4 className="font-playfair text-xl mb-3 text-charcoal">Podtytuł z charakterem</h4>
                <p className="body-breathing">
                  <HandwritingText>Odręczny akcent</HandwritingText> dla osobistych, krótkich tekstów
                </p>
              </div>
            </div>
          </div>

          <Quote author="Julia Jakubowicz" breathing>
            Joga to nie perfekcja póz, ale autentyczność momentu. 
            To złapanie śmiechu, skupienia, ulgi - prawdziwego życia.
          </Quote>
        </div>
      </section>

      <Divider />

      {/* Breathing Interactions Showcase */}
      <section className="py-20 px-8">
        <div className="max-w-6xl mx-auto">
          <SectionTitle warmAccent="oddychają">
            Mikro-interakcje które oddychają
          </SectionTitle>
          
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <UnifiedCard variant="breathing" padding="lg">
              <CardTitle breathing>
                Karty z oddechem
              </CardTitle>
              <BodyText>
                Subtelne animacje przypominające oddychanie. 
                Spokojne, naturalne, jak rytm życia.
              </BodyText>
            </UnifiedCard>

            <UnifiedCard variant="warm" padding="lg">
              <CardTitle>
                Ciepłe kolory
              </CardTitle>
              <BodyText>
                Rose gold zamiast zimnego złota. 
                Brzoskwiniowe gradienty zamiast ostrych kontrastów.
              </BodyText>
            </UnifiedCard>

            <UnifiedCard variant="elevated" padding="lg">
              <CardTitle>
                Więcej przestrzeni
              </CardTitle>
              <BodyText breathing>
                Zwiększone marginesy, większa inter-linia, 
                więcej miejsca na <HandwritingText>oddech</HandwritingText>.
              </BodyText>
            </UnifiedCard>
          </div>

          <div className="text-center space-y-6">
            <div className="space-x-4">
              <UnifiedButton variant="primary">
                Główny przycisk
              </UnifiedButton>
              <UnifiedButton variant="breathing">
                Oddychający przycisk
              </UnifiedButton>
              <UnifiedButton variant="minimal">
                Ciepłe podkreślenie
              </UnifiedButton>
            </div>
          </div>
        </div>
      </section>

      <WarmDivider />

      {/* Color Palette Showcase */}
      <section className="py-20 px-8">
        <div className="max-w-6xl mx-auto">
          <SectionTitle>
            Paleta Kolorów: Od Ekskluzywności do Ciepła
          </SectionTitle>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="font-playfair text-2xl mb-6 text-charcoal">Przed: Zimna Ekskluzywność</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-900 border"></div>
                  <span className="text-sm">Zimna czerń</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-600 border"></div>
                  <span className="text-sm">Chłodne szarości</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-yellow-600 border"></div>
                  <span className="text-sm">Zimne złoto</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-playfair text-2xl mb-6 text-charcoal">Po: Ciepła Elegancja</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-charcoal border"></div>
                  <span className="text-sm">Ciepły charcoal</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-rose-gold border"></div>
                  <span className="text-sm">Rose gold</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-dawn-peach border"></div>
                  <span className="text-sm">Brzoskwiniowy świt</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-warm-sand border"></div>
                  <span className="text-sm">Ciepły piasek</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Divider />

      {/* Photography Guidelines */}
      <section className="py-20 px-8">
        <div className="max-w-6xl mx-auto">
          <SectionTitle warmAccent="Autentyczność">
            Fotografia: Od Perfekcji do Autentyczność
          </SectionTitle>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <CardTitle>
                Nowe podejście do zdjęć
              </CardTitle>
              <BodyText breathing>
                <strong className="warm-underline">Zamiast perfekcyjnych póz:</strong> ZŁAP MOMENT 
                (śmiech, skupienie, ulga)
              </BodyText>
              <BodyText breathing>
                <strong className="warm-underline">Dodaj ZŁOTĄ GODZINĘ:</strong> wszystkie zdjęcia 
                w ciepłym świetle
              </BodyText>
              <BodyText breathing>
                <strong className="warm-underline">Pokazuj DETALE:</strong> dłonie na macie, 
                krople potu, zamknięte oczy
              </BodyText>
              <BodyText breathing>
                <strong className="warm-underline">Filtr:</strong> jasny, ciepły, 
                lekko rozmyty jak <HandwritingText>wspomnienie</HandwritingText>
              </BodyText>
            </div>
            
            <div className="bg-gradient-to-br from-dawn-cream to-dawn-peach p-8 rounded-none">
              <h4 className="font-playfair text-xl mb-4 text-charcoal">Efekt</h4>
              <Quote breathing={false}>
                'chcę tam być' zamiast 'podziwiam z daleka'
              </Quote>
              <BodyText className="text-center">
                Zachowaj estetykę ale pokaż <span className="warm-underline active">AUTENTYCZNOŚĆ</span>
              </BodyText>
            </div>
          </div>
        </div>
      </section>

      <WarmDivider />

      {/* Final CTA */}
      <section className="py-20 px-8 text-center">
        <div className="max-w-4xl mx-auto">
          <SectionTitle>
            Transformacja Kompletna
          </SectionTitle>
          <BodyText className="text-xl mb-12" breathing>
            Od <strong>'ekskluzywnego luksusu'</strong> do <HandwritingText className="text-2xl">'ciepłej elegancji'</HandwritingText>
          </BodyText>
          
          <div className="space-x-6">
            <UnifiedButton variant="primary" size="lg">
              Zobacz w akcji
            </UnifiedButton>
            <UnifiedButton variant="breathing" size="lg">
              Poczuj różnicę
            </UnifiedButton>
          </div>
        </div>
      </section>
    </div>
  );
}