/**
 * ♿ ACCESSIBILITY TESTING UTILITIES
 * 
 * Automated accessibility testing and validation tools
 * for WCAG 2.1 AA compliance
 */

/**
 * Calculate color contrast ratio between two colors
 * @param {string} foreground - Foreground color (hex, rgb, or hsl)
 * @param {string} background - Background color (hex, rgb, or hsl)
 * @returns {number} Contrast ratio (1-21)
 */
export function calculateContrastRatio(foreground, background) {
  const getLuminance = (color) => {
    // Convert color to RGB values
    const rgb = hexToRgb(color) || parseRgb(color);
    if (!rgb) return 0;

    // Convert to relative luminance
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const l1 = getLuminance(foreground);
  const l2 = getLuminance(background);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);

  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Parse RGB color string
 */
function parseRgb(rgb) {
  const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  return match ? {
    r: parseInt(match[1]),
    g: parseInt(match[2]),
    b: parseInt(match[3])
  } : null;
}

/**
 * Check if contrast ratio meets WCAG standards
 * @param {number} ratio - Contrast ratio
 * @param {string} level - 'AA' or 'AAA'
 * @param {boolean} isLargeText - Is the text large (18pt+ or 14pt+ bold)
 * @returns {boolean} Whether the ratio meets the standard
 */
export function meetsContrastStandard(ratio, level = 'AA', isLargeText = false) {
  const standards = {
    AA: { normal: 4.5, large: 3.0 },
    AAA: { normal: 7.0, large: 4.5 }
  };

  const threshold = isLargeText ? standards[level].large : standards[level].normal;
  return ratio >= threshold;
}

/**
 * Test all text elements on page for contrast compliance
 * @returns {Array} Array of contrast test results
 */
export function testPageContrast() {
  const results = [];
  const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a, button, label, input, textarea');

  textElements.forEach((element, index) => {
    const styles = window.getComputedStyle(element);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // Skip if background is transparent
    if (backgroundColor === 'rgba(0, 0, 0, 0)' || backgroundColor === 'transparent') {
      return;
    }

    const ratio = calculateContrastRatio(color, backgroundColor);
    const fontSize = parseFloat(styles.fontSize);
    const fontWeight = styles.fontWeight;
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));

    results.push({
      element,
      index,
      color,
      backgroundColor,
      ratio: Math.round(ratio * 100) / 100,
      meetsAA: meetsContrastStandard(ratio, 'AA', isLargeText),
      meetsAAA: meetsContrastStandard(ratio, 'AAA', isLargeText),
      isLargeText,
      fontSize,
      fontWeight,
      text: element.textContent.trim().substring(0, 50)
    });
  });

  return results;
}

/**
 * Test keyboard navigation
 * @returns {Object} Keyboard navigation test results
 */
export function testKeyboardNavigation() {
  const focusableElements = document.querySelectorAll(
    'a[href], button, textarea, input, select, details, [tabindex]:not([tabindex="-1"])'
  );

  const results = {
    totalFocusable: focusableElements.length,
    withoutTabIndex: 0,
    withNegativeTabIndex: 0,
    missingFocusIndicator: 0,
    elements: []
  };

  focusableElements.forEach((element, index) => {
    const tabIndex = element.getAttribute('tabindex');
    const styles = window.getComputedStyle(element);
    const hasFocusIndicator = styles.outline !== 'none' || styles.boxShadow !== 'none';

    if (!tabIndex) results.withoutTabIndex++;
    if (tabIndex === '-1') results.withNegativeTabIndex++;
    if (!hasFocusIndicator) results.missingFocusIndicator++;

    results.elements.push({
      element,
      index,
      tagName: element.tagName.toLowerCase(),
      tabIndex,
      hasFocusIndicator,
      text: element.textContent.trim().substring(0, 30)
    });
  });

  return results;
}

/**
 * Test images for alt text
 * @returns {Object} Image accessibility test results
 */
export function testImageAccessibility() {
  const images = document.querySelectorAll('img');
  const results = {
    total: images.length,
    missingAlt: 0,
    emptyAlt: 0,
    decorativeImages: 0,
    goodAlt: 0,
    images: []
  };

  images.forEach((img, index) => {
    const alt = img.getAttribute('alt');
    const isDecorative = img.getAttribute('role') === 'presentation' || 
                        img.getAttribute('aria-hidden') === 'true';

    let status = 'good';
    if (alt === null) {
      status = 'missing';
      results.missingAlt++;
    } else if (alt === '') {
      if (isDecorative) {
        status = 'decorative';
        results.decorativeImages++;
      } else {
        status = 'empty';
        results.emptyAlt++;
      }
    } else {
      results.goodAlt++;
    }

    results.images.push({
      element: img,
      index,
      src: img.src,
      alt,
      status,
      isDecorative,
      width: img.width,
      height: img.height
    });
  });

  return results;
}

/**
 * Test ARIA labels and roles
 * @returns {Object} ARIA test results
 */
export function testAriaLabels() {
  const elementsWithAria = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
  const results = {
    total: elementsWithAria.length,
    missingLabels: 0,
    invalidRoles: 0,
    elements: []
  };

  const validRoles = [
    'alert', 'alertdialog', 'application', 'article', 'banner', 'button', 'cell', 'checkbox',
    'columnheader', 'combobox', 'complementary', 'contentinfo', 'definition', 'dialog',
    'directory', 'document', 'feed', 'figure', 'form', 'grid', 'gridcell', 'group',
    'heading', 'img', 'link', 'list', 'listbox', 'listitem', 'log', 'main', 'marquee',
    'math', 'menu', 'menubar', 'menuitem', 'menuitemcheckbox', 'menuitemradio', 'navigation',
    'none', 'note', 'option', 'presentation', 'progressbar', 'radio', 'radiogroup',
    'region', 'row', 'rowgroup', 'rowheader', 'scrollbar', 'search', 'searchbox',
    'separator', 'slider', 'spinbutton', 'status', 'switch', 'tab', 'table', 'tablist',
    'tabpanel', 'term', 'textbox', 'timer', 'toolbar', 'tooltip', 'tree', 'treegrid',
    'treeitem'
  ];

  elementsWithAria.forEach((element, index) => {
    const ariaLabel = element.getAttribute('aria-label');
    const ariaLabelledby = element.getAttribute('aria-labelledby');
    const ariaDescribedby = element.getAttribute('aria-describedby');
    const role = element.getAttribute('role');

    let hasLabel = !!(ariaLabel || ariaLabelledby || element.textContent.trim());
    let hasValidRole = !role || validRoles.includes(role);

    if (!hasLabel) results.missingLabels++;
    if (!hasValidRole) results.invalidRoles++;

    results.elements.push({
      element,
      index,
      tagName: element.tagName.toLowerCase(),
      ariaLabel,
      ariaLabelledby,
      ariaDescribedby,
      role,
      hasLabel,
      hasValidRole,
      text: element.textContent.trim().substring(0, 30)
    });
  });

  return results;
}

/**
 * Run comprehensive accessibility audit
 * @returns {Object} Complete accessibility audit results
 */
export function runAccessibilityAudit() {
  console.log('🔍 Running accessibility audit...');
  
  const results = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    contrast: testPageContrast(),
    keyboard: testKeyboardNavigation(),
    images: testImageAccessibility(),
    aria: testAriaLabels()
  };

  // Generate summary
  results.summary = {
    contrastIssues: results.contrast.filter(r => !r.meetsAA).length,
    keyboardIssues: results.keyboard.missingFocusIndicator,
    imageIssues: results.images.missingAlt + results.images.emptyAlt,
    ariaIssues: results.aria.missingLabels + results.aria.invalidRoles,
    totalIssues: 0
  };

  results.summary.totalIssues = 
    results.summary.contrastIssues +
    results.summary.keyboardIssues +
    results.summary.imageIssues +
    results.summary.ariaIssues;

  console.log('✅ Accessibility audit complete:', results.summary);
  return results;
}

/**
 * Generate accessibility report
 * @param {Object} auditResults - Results from runAccessibilityAudit
 * @returns {string} HTML report
 */
export function generateAccessibilityReport(auditResults) {
  const { summary, contrast, keyboard, images, aria } = auditResults;
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
      <h1>🔍 Accessibility Audit Report</h1>
      <p><strong>URL:</strong> ${auditResults.url}</p>
      <p><strong>Date:</strong> ${new Date(auditResults.timestamp).toLocaleString()}</p>
      
      <h2>📊 Summary</h2>
      <ul>
        <li>Contrast Issues: ${summary.contrastIssues}</li>
        <li>Keyboard Issues: ${summary.keyboardIssues}</li>
        <li>Image Issues: ${summary.imageIssues}</li>
        <li>ARIA Issues: ${summary.ariaIssues}</li>
        <li><strong>Total Issues: ${summary.totalIssues}</strong></li>
      </ul>
      
      ${summary.contrastIssues > 0 ? `
        <h2>🎨 Contrast Issues</h2>
        <ul>
          ${contrast.filter(r => !r.meetsAA).map(r => 
            `<li>Ratio: ${r.ratio}:1 (needs 4.5:1) - "${r.text}"</li>`
          ).join('')}
        </ul>
      ` : ''}
      
      ${summary.imageIssues > 0 ? `
        <h2>🖼️ Image Issues</h2>
        <ul>
          ${images.images.filter(i => i.status === 'missing' || i.status === 'empty').map(i => 
            `<li>${i.status === 'missing' ? 'Missing' : 'Empty'} alt text: ${i.src}</li>`
          ).join('')}
        </ul>
      ` : ''}
      
      <p style="margin-top: 40px; padding: 20px; background: var(--whisper); border-radius: 8px;">
        <strong>Note:</strong> This is an automated audit. Manual testing with screen readers and keyboard navigation is also recommended.
      </p>
    </div>
  `;
}
