'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

// 🚀 CONVERSION OPTIMIZATION SYSTEM
// Advanced A/B testing and conversion tracking for maximum ROI

// ========================================
// 1. A/B TEST CONFIGURATIONS
// ========================================

const AB_TESTS = {
    hero_cta: {
      name: 'Hero CTA Button Test',
      variants: {
        control: {
          text: 'Zarezerwuj Retreat',
          color: '#C9A575',
          size: 'large'
        },
        variant_a: {
          text: 'Rozpocznij Transformację',
          color: '#B8956A',
          size: 'large'
        },
        variant_b: {
          text: 'Odkryj Swoją Jogę',
          color: '#D4B885',
          size: 'extra-large'
        },
        variant_c: {
          text: '🧘‍♀️ <PERSON>are<PERSON>wu<PERSON>',
          color: '#C9A575',
          size: 'large'
        }
      },
      trafficSplit: 25, // 25% each variant
      conversionGoal: 'booking_click',
      status: 'active',
      startDate: '2024-01-15',
      endDate: '2024-02-15'
    },
    
    pricing_display: {
      name: 'Pricing Display Test',
      variants: {
        control: {
          format: 'from 3400 PLN',
          emphasis: 'price'
        },
        variant_a: {
          format: 'od 3400 PLN (wszystko wliczone)',
          emphasis: 'value'
        },
        variant_b: {
          format: '3400 PLN - pełna cena bez ukrytych kosztów',
          emphasis: 'transparency'
        },
        variant_c: {
          format: '3400 PLN / 7 dni = 485 PLN dziennie',
          emphasis: 'daily_value'
        }
      },
      trafficSplit: 25,
      conversionGoal: 'pricing_page_visit',
      status: 'active'
    },
    
    testimonial_format: {
      name: 'Testimonial Format Test',
      variants: {
        control: {
          format: 'carousel',
          display: 'text_only'
        },
        variant_a: {
          format: 'grid',
          display: 'text_with_photos'
        },
        variant_b: {
          format: 'video',
          display: 'video_testimonials'
        },
        variant_c: {
          format: 'stories',
          display: 'instagram_style'
        }
      },
      trafficSplit: 25,
      conversionGoal: 'testimonial_interaction',
      status: 'planning'
    },
    
    contact_form: {
      name: 'Contact Form Optimization',
      variants: {
        control: {
          fields: ['name', 'email', 'phone', 'message'],
          steps: 1
        },
        variant_a: {
          fields: ['name', 'email', 'retreat_interest'],
          steps: 1
        },
        variant_b: {
          fields: ['name', 'email', 'phone', 'retreat_interest', 'budget', 'dates'],
          steps: 2
        },
        variant_c: {
          fields: ['email', 'retreat_interest'],
          steps: 1
        }
      },
      trafficSplit: 25,
      conversionGoal: 'form_submission',
      status: 'active'
    }
  };

// ========================================
// 2. USER SEGMENTATION
// ========================================

const USER_SEGMENTS = {
    first_time_visitor: {
      criteria: {
        visitCount: 1,
        sessionDuration: { min: 0, max: 300 }
      },
      personalizedContent: {
        hero_message: 'Witaj! Odkryj magię retreatów jogi z BAKASANA',
        cta_text: 'Dowiedz się więcej',
        testimonial_focus: 'beginner_friendly',
        pricing_emphasis: 'value_proposition'
      },
      conversionOptimization: {
        showExitIntent: true,
        offerNewsletter: true,
        displayTrustBadges: true
      }
    },
    
    returning_visitor: {
      criteria: {
        visitCount: { min: 2, max: 5 },
        sessionDuration: { min: 180 }
      },
      personalizedContent: {
        hero_message: 'Witaj ponownie! Gotowy na transformację?',
        cta_text: 'Sprawdź dostępne terminy',
        testimonial_focus: 'transformation_stories',
        pricing_emphasis: 'early_bird_discount'
      },
      conversionOptimization: {
        showUrgency: true,
        offerCallback: true,
        displayAvailability: true
      }
    },
    
    engaged_visitor: {
      criteria: {
        visitCount: { min: 6 },
        sessionDuration: { min: 300 },
        pagesViewed: { min: 3 }
      },
      personalizedContent: {
        hero_message: 'Czas na decyzję! Twoje miejsce czeka',
        cta_text: 'Zarezerwuj teraz',
        testimonial_focus: 'detailed_experiences',
        pricing_emphasis: 'limited_availability'
      },
      conversionOptimization: {
        showPersonalizedOffer: true,
        offerDirectCall: true,
        displayRecommendations: true
      }
    },
    
    mobile_user: {
      criteria: {
        deviceType: 'mobile'
      },
      personalizedContent: {
        hero_message: 'Retreaty jogi na wyciągnięcie ręki',
        cta_text: 'Zadzwoń teraz',
        layout: 'mobile_optimized',
        contact_preference: 'whatsapp'
      },
      conversionOptimization: {
        showClickToCall: true,
        offerWhatsApp: true,
        simplifyForms: true
      }
    },
    
    high_intent: {
      criteria: {
        visitedPages: ['program', 'rezerwacja', 'ceny'],
        timeOnSite: { min: 600 }
      },
      personalizedContent: {
        hero_message: 'Ostatni krok do Twojej transformacji',
        cta_text: 'Finalizuj rezerwację',
        testimonial_focus: 'recent_participants',
        pricing_emphasis: 'payment_plans'
      },
      conversionOptimization: {
        showLiveChat: true,
        offerPhoneConsultation: true,
        displayGuarantees: true
      }
    }
  };

// ========================================
// 3. CONVERSION FUNNELS
// ========================================

const CONVERSION_FUNNELS = {
    main_funnel: {
      name: 'Main Retreat Booking Funnel',
      steps: [
        {
          name: 'Homepage Visit',
          url: '/',
          conversionRate: 85,
          dropOffReasons: ['slow_loading', 'unclear_value_prop']
        },
        {
          name: 'Program Page',
          url: '/program',
          conversionRate: 45,
          dropOffReasons: ['too_much_info', 'no_clear_cta']
        },
        {
          name: 'Pricing Check',
          url: '/rezerwacja',
          conversionRate: 65,
          dropOffReasons: ['price_shock', 'no_payment_options']
        },
        {
          name: 'Contact Form',
          url: '/kontakt',
          conversionRate: 35,
          dropOffReasons: ['form_too_long', 'no_trust_signals']
        },
        {
          name: 'Booking Confirmation',
          url: '/booking-success',
          conversionRate: 78,
          dropOffReasons: ['payment_issues', 'last_minute_doubts']
        }
      ]
    },
    
    newsletter_funnel: {
      name: 'Newsletter Signup Funnel',
      steps: [
        {
          name: 'Newsletter Popup',
          trigger: 'exit_intent',
          conversionRate: 12,
          variants: ['discount_offer', 'free_guide', 'early_access']
        },
        {
          name: 'Email Confirmation',
          conversionRate: 89,
          optimizations: ['welcome_series', 'immediate_value']
        },
        {
          name: 'First Email Open',
          conversionRate: 45,
          optimizations: ['subject_line_tests', 'send_time_optimization']
        },
        {
          name: 'Click to Website',
          conversionRate: 23,
          optimizations: ['compelling_content', 'clear_cta']
        }
      ]
    },
    
    social_media_funnel: {
      name: 'Social Media to Booking',
      steps: [
        {
          name: 'Social Media Click',
          sources: ['instagram', 'facebook', 'tiktok'],
          conversionRate: 35,
          optimizations: ['landing_page_match', 'mobile_optimization']
        },
        {
          name: 'Content Engagement',
          conversionRate: 55,
          optimizations: ['video_content', 'user_generated_content']
        },
        {
          name: 'Website Visit',
          conversionRate: 40,
          optimizations: ['social_proof', 'instagram_integration']
        },
        {
          name: 'Booking Interest',
          conversionRate: 28,
          optimizations: ['social_testimonials', 'influencer_mentions']
        }
      ]
    }
  };

// ========================================
// 4. HEATMAP & BEHAVIOR TRACKING
// ========================================

const HEATMAP_ZONES = {
    homepage: {
      hero_section: {
        clicks: 45,
        attention_time: 3.2,
        scroll_depth: 95,
        conversion_rate: 8.5
      },
      testimonials: {
        clicks: 23,
        attention_time: 2.8,
        scroll_depth: 78,
        conversion_rate: 12.3
      },
      program_preview: {
        clicks: 67,
        attention_time: 4.1,
        scroll_depth: 85,
        conversion_rate: 15.7
      },
      cta_buttons: {
        clicks: 89,
        attention_time: 1.5,
        scroll_depth: 100,
        conversion_rate: 22.4
      }
    },
    
    program_page: {
      itinerary: {
        clicks: 78,
        attention_time: 5.2,
        scroll_depth: 92,
        conversion_rate: 18.9
      },
      pricing: {
        clicks: 156,
        attention_time: 3.8,
        scroll_depth: 88,
        conversion_rate: 25.6
      },
      testimonials: {
        clicks: 34,
        attention_time: 2.1,
        scroll_depth: 65,
        conversion_rate: 11.2
      },
      booking_form: {
        clicks: 89,
        attention_time: 2.5,
        scroll_depth: 95,
        conversion_rate: 35.4
      }
    }
  };

export default function ConversionOptimization() {
  const pathname = usePathname();
  const [testVariant, setTestVariant] = useState('control');
  const [conversionData, setConversionData] = useState({});
  const [userSegment, setUserSegment] = useState('');
  const [personalizedContent, setPersonalizedContent] = useState({});

  // ========================================
  // 5. PERSONALIZATION ENGINE
  // ========================================
  
  useEffect(() => {
    const initializePersonalization = () => {
      const userData = {
        visitCount: parseInt(localStorage.getItem('visitCount') || '1'),
        sessionDuration: Date.now() - parseInt(sessionStorage.getItem('sessionStart') || Date.now()),
        deviceType: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
        referrer: document.referrer,
        pagesViewed: JSON.parse(localStorage.getItem('pagesViewed') || '[]'),
        previousVisits: JSON.parse(localStorage.getItem('previousVisits') || '[]')
      };
      
      // Update visit count
      localStorage.setItem('visitCount', (userData.visitCount + 1).toString());
      
      // Update pages viewed
      const pagesViewed = userData.pagesViewed;
      if (!pagesViewed.includes(pathname)) {
        pagesViewed.push(pathname);
        localStorage.setItem('pagesViewed', JSON.stringify(pagesViewed));
      }
      
      // Determine user segment
      let segment = 'first_time_visitor';
      
      if (userData.visitCount >= 6 && userData.sessionDuration > 300000 && pagesViewed.length >= 3) {
        segment = 'engaged_visitor';
      } else if (userData.visitCount >= 2 && userData.sessionDuration > 180000) {
        segment = 'returning_visitor';
      } else if (userData.deviceType === 'mobile') {
        segment = 'mobile_user';
      }
      
      // Check for high intent
      const highIntentPages = ['program', 'rezerwacja', 'ceny', 'kontakt'];
      if (highIntentPages.some(page => pagesViewed.includes(page)) && userData.sessionDuration > 600000) {
        segment = 'high_intent';
      }
      
      setUserSegment(segment);
      
      // Set personalized content
      if (USER_SEGMENTS[segment]) {
        setPersonalizedContent(USER_SEGMENTS[segment].personalizedContent);
      }
      
      // A/B test assignment
      const testVariant = assignABTestVariant(userData);
      setTestVariant(testVariant);
    };
    
    const assignABTestVariant = (userData) => {
      const activeTests = Object.entries(AB_TESTS).filter(([key, test]) => test.status === 'active');
      const assignments = {};
      
      activeTests.forEach(([testName, test]) => {
        const hash = simpleHash(userData.visitCount + testName);
        const variants = Object.keys(test.variants);
        const variantIndex = hash % variants.length;
        assignments[testName] = variants[variantIndex];
      });
      
      return assignments;
    };
    
    const simpleHash = (str) => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash);
    };
    
    initializePersonalization();
  }, [pathname]);

  // ========================================
  // 6. CONVERSION TRACKING
  // ========================================
  
  const trackConversion = (eventName, eventData) => {
    const conversionEvent = {
      event: eventName,
      timestamp: new Date().toISOString(),
      page: pathname,
      userSegment: userSegment,
      testVariant: testVariant,
      personalizedContent: personalizedContent,
      ...eventData
    };
    
    // Track in analytics
    if (window.gtag) {
      window.gtag('event', 'conversion', {
        event_category: 'Conversion',
        event_label: eventName,
        value: eventData.value || 1,
        custom_parameter_1: userSegment,
        custom_parameter_2: JSON.stringify(testVariant),
        custom_parameter_3: pathname
      });
    }
    
    if (window.mixpanel) {
      window.mixpanel.track('Conversion', conversionEvent);
    }
    
    if (window.fbq) {
      window.fbq('track', 'Purchase', {
        value: eventData.value || 0,
        currency: 'PLN',
        content_name: eventName,
        content_category: 'Conversion'
      });
    }
    
    // Store conversion data
    const conversions = JSON.parse(localStorage.getItem('conversions') || '[]');
    conversions.push(conversionEvent);
    localStorage.setItem('conversions', JSON.stringify(conversions));
    
    setConversionData(prev => ({
      ...prev,
      [eventName]: conversionEvent
    }));
  };

  // ========================================
  // 7. GLOBAL FUNCTIONS
  // ========================================
  
  useEffect(() => {
    // Make conversion tracking available globally
    window.trackConversion = trackConversion;
    
    // Track page view conversion
    trackConversion('page_view', {
      page: pathname,
      value: 1
    });
    
    // Set up form tracking
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        trackConversion('form_submission', {
          formId: form.id,
          formType: form.dataset.type || 'contact',
          value: 50
        });
      });
    });
    
    // Set up CTA button tracking
    const ctaButtons = document.querySelectorAll('[data-cta]');
    ctaButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        trackConversion('cta_click', {
          buttonText: button.textContent,
          buttonType: button.dataset.cta,
          value: 25
        });
      });
    });
    
    // Set up exit intent tracking
    let exitIntentShown = false;
    const handleExitIntent = (e) => {
      if (e.clientY <= 0 && !exitIntentShown) {
        exitIntentShown = true;
        trackConversion('exit_intent', {
          timeOnPage: Date.now() - parseInt(sessionStorage.getItem('sessionStart') || Date.now()),
          value: 10
        });
      }
    };
    
    document.addEventListener('mouseleave', handleExitIntent);
    
    return () => {
      document.removeEventListener('mouseleave', handleExitIntent);
    };
  }, [pathname, userSegment, testVariant]);

  // Component doesn't render anything - it's for tracking only
  return null;
}

// Export functions for external use
export {
  AB_TESTS,
  USER_SEGMENTS,
  CONVERSION_FUNNELS,
  HEATMAP_ZONES
};