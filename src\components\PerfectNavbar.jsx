'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NavLink } from '@/components/ui/UnifiedTypography';
import { mainNavItems } from '@/data/navigationLinks';

export default function PerfectNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const pathname = usePathname();

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    setScrolled(currentScrollY > 20);
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Zamknij menu mobilne przy zmianie ścieżki
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  const isActiveLink = useCallback((href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  }, [pathname]);

  const handleDropdownToggle = useCallback((index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  }, [activeDropdown]);

  // Obsługa klawiatury dla dropdown
  const handleKeyDown = useCallback((e, index) => {
    if (e.key === 'Escape') {
      setActiveDropdown(null);
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleDropdownToggle(index);
    }
  }, [handleDropdownToggle]);

  const handleMouseEnter = useCallback((index) => {
    // Zabezpieczenie przed błędami SSR i użycie matchMedia API dla lepszej synchronizacji z Tailwind breakpointami
    if (typeof window !== 'undefined' && window.matchMedia('(min-width: 1024px)').matches) {
      setActiveDropdown(index);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (typeof window !== 'undefined' && window.matchMedia('(min-width: 1024px)').matches) {
      setActiveDropdown(null);
    }
  }, []);

  return (
    <>
      <nav
        className={`navbar-optimized ${
          scrolled ? 'navbar-scrolled' : 'navbar-transparent'
        }`}
        role="navigation"
        aria-label="Główna nawigacja"
      >
        <div className="mx-auto px-6 lg:px-12 xl:px-16">
          <div className="flex items-center justify-between h-20">
            
            {/* Logo - Gradient with glow effect */}
            <Link
              href="/"
              className="group relative font-primary font-light transition-all duration-normal text-subtitle tracking-ultra"
              style={{
                background: 'linear-gradient(135deg, var(--temple-gold) 0%, var(--sand) 50%, var(--amber) 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                filter: 'drop-shadow(0 0 8px rgba(184, 147, 92, 0.3))'
              }}
            >
              BAKASANA
              <span
                className="absolute -bottom-1 left-0 w-0 h-[1px] transition-all duration-normal group-hover:w-full"
                style={{
                  background: 'linear-gradient(90deg, var(--temple-gold), var(--sand))',
                  boxShadow: '0 0 4px rgba(184, 147, 92, 0.5)'
                }}
              ></span>
            </Link>

            {/* Desktop Menu - Enhanced spacing and animations */}
            <div className="hidden lg:flex items-center space-x-10">
              {mainNavItems.slice(1).map((item, index) => (
                <div
                  key={item.href}
                  className="relative group"
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={handleMouseLeave}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                >
                  <Link
                    href={item.href}
                    className="relative group flex items-center space-x-2 py-2"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)}
                      className={`transition-all duration-300 hover:scale-105 ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-3 py-1 rounded-full' 
                          : ''
                      }`}
                    >
                      {item.label}
                    </NavLink>
                    
                    {/* Dropdown arrow */}
                    {item.dropdown && (
                      <svg 
                        className={`w-3 h-3 transition-all duration-300 ${
                          activeDropdown === index ? 'rotate-180 text-enterprise-brown' : 'text-charcoal-light'
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                    
                    {/* Aktywny wskaźnik - elegancka linia */}
                    {isActiveLink(item.href) && (
                      <span className="absolute -bottom-1 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-enterprise-brown to-transparent opacity-80 rounded-full animate-pulse-gentle"></span>
                    )}
                    
                    {/* Hover efekt - subtelna linia */}
                    <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-[1px] bg-enterprise-brown/60 transition-all duration-500 group-hover:w-3/4"></span>
                  </Link>

                  {/* Enhanced Dropdown Menu */}
                  {item.dropdown && (
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-6 z-50
                      bg-white/98 backdrop-blur-xl shadow-premium-shadow border border-enterprise-brown/10
                      transition-all duration-500 min-w-[320px] rounded-2xl overflow-hidden
                      before:absolute before:top-0 before:left-1/2 before:transform before:-translate-x-1/2 before:-translate-y-1
                      before:w-4 before:h-4 before:bg-white/98 before:rotate-45 before:border-l before:border-t before:border-enterprise-brown/10
                      ${activeDropdown === index
                        ? 'opacity-100 visible translate-y-0 scale-100'
                        : 'opacity-0 invisible translate-y-4 scale-95 pointer-events-none'
                      }`}
                    >
                      <div className="py-6">
                        {item.dropdown.map((section, sectionIndex) => (
                          <div key={sectionIndex} className={sectionIndex > 0 ? 'mt-6 pt-6 border-t border-enterprise-brown/10' : ''}>
                            <div className="px-6 mb-3">
                              <h4 className="font-cormorant font-light text-enterprise-brown text-xl tracking-wide flex items-center">
                                <span className="w-2 h-2 bg-enterprise-brown/30 rounded-full mr-3"></span>
                                {section.header}
                              </h4>
                            </div>
                            <div className="space-y-1">
                              {section.items.map((dropdownItem, itemIndex) => (
                                <Link
                                  key={dropdownItem.href}
                                  href={dropdownItem.href}
                                  className="block px-6 py-3 hover:bg-whisper/70 transition-all duration-300 group relative overflow-hidden"
                                  style={{ animationDelay: `${itemIndex * 50}ms` }}
                                >
                                  <div className="absolute left-0 top-0 w-1 h-full bg-enterprise-brown transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top"></div>
                                  <NavLink className="text-sm group-hover:text-enterprise-brown group-hover:translate-x-2 transition-all duration-300">
                                    {dropdownItem.label}
                                  </NavLink>
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Book Session Button - Desktop */}
            <div className="hidden lg:block">
              <a
                href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative px-6 py-2 font-lato font-light text-sm uppercase tracking-[2px] transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-temple-gold focus:ring-opacity-50 rounded-sm bg-gradient-to-br from-temple-gold to-amber text-white hover:-translate-y-0.5 hover:shadow-lg"
                style={{
                  boxShadow: '0 4px 12px rgba(184, 147, 92, 0.3)',
                }}
                aria-label="Skontaktuj się przez WhatsApp w sprawie retreatów jogi"
              >
                BOOK SESSION
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 shimmer-effect" />
              </a>
            </div>

            {/* Mobile Menu Button - Elegancki hamburger */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-3 text-enterprise-brown hover:text-temple-gold transition-all duration-normal focus:outline-none focus:ring-2 focus:ring-temple-gold focus:ring-opacity-50 rounded-md"
              aria-label={isMenuOpen ? "Zamknij menu" : "Otwórz menu"}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? 'rotate-45 translate-y-[4px]' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 ${
                  isMenuOpen ? 'opacity-0' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? '-rotate-45 -translate-y-[4px]' : ''
                }`}></span>
              </div>
            </button>
          </div>
        </div>

        {/* Optimized Mobile Menu */}
        <div
          id="mobile-menu"
          className={`lg:hidden absolute top-full left-0 right-0 z-90
          bg-sanctuary/95 backdrop-blur-[25px] border-b border-enterprise-brown/8
          transition-all duration-300 ease-out ${
          isMenuOpen
            ? 'opacity-100 translate-y-0 max-h-screen visible'
            : 'opacity-0 -translate-y-2 max-h-0 invisible overflow-hidden'
        }`}>
          <div className="px-6 py-8 space-y-8">
            {mainNavItems.slice(1).map((item, index) => (
              <div
                key={item.href}
                className={`space-y-3 ${isMenuOpen ? 'mobile-menu-item' : ''}`}
                style={{
                  animationDelay: isMenuOpen ? `${index * 100}ms` : '0ms'
                }}
              >
                <div className="flex items-center justify-between group">
                  <Link
                    href={item.href}
                    onClick={() => !item.dropdown && setIsMenuOpen(false)}
                    className="flex-1 relative"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)} 
                      className={`text-lg transition-all duration-300 ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-4 py-2 rounded-full inline-block' 
                          : ''
                      }`}
                    >
                      {item.label}
                      {isActiveLink(item.href) && !item.highlight && (
                        <span className="inline-block ml-3 w-2 h-2 bg-enterprise-brown rounded-full opacity-80 animate-pulse"></span>
                      )}
                    </NavLink>
                    
                    {/* Mobile active indicator */}
                    {isActiveLink(item.href) && !item.highlight && (
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-1 h-6 bg-enterprise-brown rounded-full"></div>
                    )}
                  </Link>
                  
                  {/* Mobile dropdown toggle */}
                  {item.dropdown && (
                    <button
                      onClick={() => handleDropdownToggle(index)}
                      className="p-3 text-enterprise-brown/60 hover:text-enterprise-brown hover:bg-whisper/50 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-temple-gold focus:ring-opacity-50"
                      aria-label={`${activeDropdown === index ? 'Zamknij' : 'Otwórz'} podmenu ${item.label}`}
                      aria-expanded={activeDropdown === index}
                    >
                      <svg 
                        className={`w-5 h-5 transition-transform duration-300 ${
                          activeDropdown === index ? 'rotate-180' : ''
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  )}
                </div>
                
                {/* Mobile dropdown content */}
                {item.dropdown && (
                  <div className={`pl-6 space-y-4 transition-all duration-500 ${
                    activeDropdown === index 
                      ? 'opacity-100 max-h-96 translate-y-0' 
                      : 'opacity-0 max-h-0 -translate-y-2 overflow-hidden'
                  }`}>
                    {item.dropdown.map((section, sectionIndex) => (
                      <div key={sectionIndex} className="space-y-3">
                        <div className="flex items-center">
                          <div className="w-3 h-[1px] bg-enterprise-brown/30 mr-3"></div>
                          <h5 className="font-cormorant text-enterprise-brown text-base font-light tracking-wide">
                            {section.header}
                          </h5>
                        </div>
                        <div className="space-y-2 pl-6">
                          {section.items.map((dropdownItem, itemIndex) => (
                            <Link
                              key={dropdownItem.href}
                              href={dropdownItem.href}
                              onClick={() => setIsMenuOpen(false)}
                              className={`block py-2 group relative ${activeDropdown === index ? 'mobile-menu-item' : ''}`}
                              style={{
                                animationDelay: activeDropdown === index ? `${itemIndex * 100}ms` : '0ms'
                              }}
                            >
                              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-0 h-[1px] bg-enterprise-brown/50 group-hover:w-4 transition-all duration-300"></div>
                              <NavLink className="text-sm opacity-80 hover:opacity-100 hover:text-enterprise-brown group-hover:translate-x-6 transition-all duration-300">
                                {dropdownItem.label}
                              </NavLink>
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
            
            {/* Book Session Button - Mobile */}
            <div
              className={`pt-8 border-t border-enterprise-brown/10 ${isMenuOpen ? 'mobile-menu-item' : ''}`}
              style={{
                animationDelay: isMenuOpen ? `${mainNavItems.length * 100}ms` : '0ms'
              }}
            >
              <a
                href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full flex items-center justify-center text-body font-light tracking-widest py-4 px-8 rounded-xl hover:scale-105 transition-all duration-normal"
                style={{
                  backgroundColor: 'var(--enterprise-brown)',
                  color: 'var(--sanctuary)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = 'var(--temple-gold)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'var(--enterprise-brown)';
                }}
                aria-label="Skontaktuj się przez WhatsApp w sprawie retreatów jogi"
              >
                POROZMAWIAJMY
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Enhanced Mobile Menu Overlay */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/10 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Navbar spacing */}
      <div className="h-20"></div>


    </>
  );
}