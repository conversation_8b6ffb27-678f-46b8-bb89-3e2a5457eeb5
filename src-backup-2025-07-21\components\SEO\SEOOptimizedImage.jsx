'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { SEO_CONFIG } from '@/lib/seoManager';

/**
 * 🖼️ SEO OPTIMIZED IMAGE COMPONENT
 * 
 * Comprehensive image optimization for SEO:
 * - Descriptive alt text with keywords
 * - Structured data for images
 * - Lazy loading optimization
 * - Multiple format support (WebP, AVIF)
 * - Social media optimization
 * - Core Web Vitals optimization
 */

const SEOOptimizedImage = ({
  src,
  alt,
  title,
  width,
  height,
  priority = false,
  quality = 85,
  className = '',
  sizes = '100vw',
  fill = false,
  caption,
  credit,
  location,
  keywords = [],
  schemaType = 'ImageObject',
  loading = 'lazy',
  placeholder = 'blur',
  blurDataURL,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  
  // Generate SEO-optimized alt text
  const generateSEOAltText = () => {
    if (!alt) return '';
    
    // Enhance alt text with location and keywords if provided
    let enhancedAlt = alt;
    
    if (location) {
      enhancedAlt += ` in ${location}`;
    }
    
    if (keywords.length > 0) {
      enhancedAlt += ` - ${keywords.slice(0, 3).join(', ')}`;
    }
    
    // Ensure alt text is not too long (125 characters max for SEO)
    if (enhancedAlt.length > 125) {
      enhancedAlt = enhancedAlt.substring(0, 122) + '...';
    }
    
    return enhancedAlt;
  };
  
  // Generate image structured data
  const generateImageSchema = () => {
    const imageUrl = src.startsWith('http') ? src : `${SEO_CONFIG.siteUrl}${src}`;
    
    return {
      '@context': 'https://schema.org',
      '@type': schemaType,
      contentUrl: imageUrl,
      url: imageUrl,
      name: title || alt,
      description: alt,
      width: width,
      height: height,
      encodingFormat: getImageFormat(src),
      ...(caption && { caption }),
      ...(credit && { 
        creator: {
          '@type': 'Person',
          name: credit
        }
      }),
      ...(location && {
        contentLocation: {
          '@type': 'Place',
          name: location
        }
      }),
      ...(keywords.length > 0 && { keywords: keywords.join(', ') }),
      license: `${SEO_CONFIG.siteUrl}/license`,
      acquireLicensePage: `${SEO_CONFIG.siteUrl}/license`,
      creditText: credit || SEO_CONFIG.organization.name,
      copyrightNotice: `© ${new Date().getFullYear()} ${SEO_CONFIG.organization.name}`
    };
  };
  
  // Get image format from URL
  const getImageFormat = (url) => {
    const extension = url.split('.').pop()?.toLowerCase();
    const formatMap = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'avif': 'image/avif',
      'svg': 'image/svg+xml'
    };
    return formatMap[extension] || 'image/jpeg';
  };
  
  // Handle image load
  const handleLoad = (e) => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.(e);
    
    // Track image load for Core Web Vitals
    if (priority && typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'image_load', {
        event_category: 'Performance',
        event_label: src,
        custom_parameter_1: 'hero_image'
      });
    }
  };
  
  // Handle image error
  const handleError = (e) => {
    setHasError(true);
    setIsLoaded(false);
    onError?.(e);
    
    // Track image errors
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'image_error', {
        event_category: 'Error',
        event_label: src
      });
    }
  };
  
  // Generate responsive sizes for better performance
  const generateResponsiveSizes = () => {
    if (sizes !== '100vw') return sizes;
    
    // Default responsive sizes for better performance
    return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
  };
  
  // Generate blur data URL for placeholder
  const generateBlurDataURL = () => {
    if (blurDataURL) return blurDataURL;
    
    // Generate a simple blur data URL based on image dimensions
    const canvas = typeof window !== 'undefined' ? document.createElement('canvas') : null;
    if (!canvas) return undefined;
    
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#f3f4f6';
    ctx.fillRect(0, 0, 10, 10);
    
    return canvas.toDataURL();
  };
  
  const imageSchema = generateImageSchema();
  const optimizedAlt = generateSEOAltText();
  const responsiveSizes = generateResponsiveSizes();
  
  return (
    <figure className={`seo-optimized-image ${className}`}>
      {/* Structured Data for Image */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(imageSchema)
        }}
      />
      
      {/* Preload hint for critical images */}
      {priority && (
        <link
          rel="preload"
          as="image"
          href={src}
          imageSizes={responsiveSizes}
          imageSrcSet={`${src} 1x`}
        />
      )}
      
      {/* Main Image */}
      <div className="relative overflow-hidden">
        {fill ? (
          <Image
            src={src}
            alt={optimizedAlt}
            title={title}
            fill
            quality={quality}
            priority={priority}
            sizes={responsiveSizes}
            className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
            placeholder={placeholder}
            blurDataURL={generateBlurDataURL()}
            onLoad={handleLoad}
            onError={handleError}
            {...props}
          />
        ) : (
          <Image
            src={src}
            alt={optimizedAlt}
            title={title}
            width={width}
            height={height}
            quality={quality}
            priority={priority}
            sizes={responsiveSizes}
            loading={priority ? 'eager' : loading}
            className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
            placeholder={placeholder}
            blurDataURL={generateBlurDataURL()}
            onLoad={handleLoad}
            onError={handleError}
            {...props}
          />
        )}
        
        {/* Loading placeholder */}
        {!isLoaded && !hasError && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
        
        {/* Error placeholder */}
        {hasError && (
          <div className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500 text-sm">
            <div className="text-center">
              <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p>Image unavailable</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Caption with microdata */}
      {caption && (
        <figcaption 
          className="text-sm text-gray-600 mt-2 text-center"
          itemProp="caption"
        >
          {caption}
          {credit && (
            <span className="block text-xs text-gray-500 mt-1">
              Photo by {credit}
            </span>
          )}
        </figcaption>
      )}
      
      {/* Hidden metadata for SEO */}
      <div className="sr-only">
        {keywords.length > 0 && (
          <span itemProp="keywords">{keywords.join(', ')}</span>
        )}
        {location && (
          <span itemProp="contentLocation">{location}</span>
        )}
      </div>
    </figure>
  );
};

// Specialized components for different use cases
export const HeroImageSEO = ({ src, alt, title, location, keywords = [], ...props }) => (
  <SEOOptimizedImage
    src={src}
    alt={alt}
    title={title}
    location={location}
    keywords={['yoga retreat', 'bali', 'sri lanka', ...keywords]}
    priority={true}
    quality={90}
    schemaType="ImageObject"
    fill
    sizes="100vw"
    {...props}
  />
);

export const GalleryImageSEO = ({ src, alt, title, location, keywords = [], ...props }) => (
  <SEOOptimizedImage
    src={src}
    alt={alt}
    title={title}
    location={location}
    keywords={['yoga', 'meditation', 'retreat', ...keywords]}
    quality={85}
    schemaType="ImageObject"
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    {...props}
  />
);

export const ProfileImageSEO = ({ src, alt, title, keywords = [], ...props }) => (
  <SEOOptimizedImage
    src={src}
    alt={alt}
    title={title}
    keywords={['yoga instructor', 'julia jakubowicz', 'physiotherapist', ...keywords]}
    quality={90}
    schemaType="ImageObject"
    {...props}
  />
);

export default SEOOptimizedImage;
