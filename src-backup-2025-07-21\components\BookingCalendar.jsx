'use client';

import { useState, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ScrollReveal } from './ScrollReveal';

// Dynamic imports to avoid SSR issues
let Calendar, momentLocalizer, moment;

if (typeof window !== 'undefined') {
  try {
    const BigCalendar = require('react-big-calendar');
    Calendar = BigCalendar.Calendar;
    momentLocalizer = BigCalendar.momentLocalizer;
    moment = require('moment');
    require('moment/locale/pl');
    require('react-big-calendar/lib/css/react-big-calendar.css');
    moment.locale('pl');
  } catch (error) {
    console.warn('Calendar libraries not loaded:', error);
  }
}

// Enhanced retreat data with more details
const retreatEvents = [
  {
    id: 1,
    title: 'Bali Yoga Retreat - Wiosna 2025',
    start: new Date(2025, 2, 15), // March 15, 2025
    end: new Date(2025, 2, 22),   // March 22, 2025
    price: 2900,
    spotsLeft: 3,
    totalSpots: 12,
    status: 'available',
    level: 'wszystkie poziomy',
    location: 'Uluwatu, Bali',
    description: 'Wiosenny retreat jogowy na Bali z praktyką na plażach Uluwatu. Odkryj magię jogi w tropikalnym raju.',
    includes: ['7 nocy w hotelu 4*', 'Wszystkie posiłki wegańskie', 'Transport z/do lotniska', 'Joga 2x dziennie', 'Zwiedzanie świątyń', 'Masaż balijski'],
    instructor: 'Julia Jakubowicz',
    image: '/images/retreats/bali-spring-2025.jpg',
    earlyBirdPrice: 2600,
    earlyBirdDeadline: new Date(2025, 1, 1)
  },
  {
    id: 2,
    title: 'Bali Yoga Retreat - Maj 2025',
    start: new Date(2025, 4, 10), // May 10, 2025
    end: new Date(2025, 4, 17),   // May 17, 2025
    price: 3200,
    spotsLeft: 8,
    totalSpots: 12,
    status: 'available',
    level: 'średniozaawansowany/zaawansowany',
    location: 'Canggu, Bali',
    description: 'Majowy retreat z fokusem na advanced asany i medytację. Dla doświadczonych praktykujących.',
    includes: ['7 nocy w hotelu boutique', 'Wszystkie posiłki organiczne', 'Transport', 'Joga 2x dziennie', 'Warsztaty arm balances', 'Ceremonia kakao'],
    instructor: 'Julia Jakubowicz',
    image: '/images/retreats/bali-may-2025.jpg',
    earlyBirdPrice: 2900,
    earlyBirdDeadline: new Date(2025, 2, 15)
  },
  {
    id: 3,
    title: 'Bali Yoga Retreat - Lato 2025',
    start: new Date(2025, 6, 5),  // July 5, 2025
    end: new Date(2025, 6, 12),   // July 12, 2025
    price: 3400,
    spotsLeft: 0,
    totalSpots: 15,
    status: 'full',
    level: 'wszystkie poziomy',
    location: 'Ubud, Bali',
    description: 'Letni retreat w sercu Bali - WYPRZEDANY! Dołącz do listy oczekujących.',
    includes: ['7 nocy w eco-resort', 'Wszystkie posiłki wegańskie', 'Transport', 'Joga 2x dziennie', 'Warsztaty pranayama'],
    instructor: 'Julia Jakubowicz',
    image: '/images/retreats/bali-summer-2025.jpg',
    waitingList: true
  },
  {
    id: 4,
    title: 'Sri Lanka Yoga Retreat - Jesień 2025',
    start: new Date(2025, 8, 20), // September 20, 2025
    end: new Date(2025, 8, 27),   // September 27, 2025
    price: 2700,
    spotsLeft: 12,
    totalSpots: 15,
    status: 'available',
    level: 'wszystkie poziomy',
    location: 'Ella & Sigiriya, Sri Lanka',
    description: 'Jesienny retreat na Sri Lance - odkryj perłę Oceanu Indyjskiego. Joga w górach i przy świątyniach.',
    includes: ['7 nocy w hotelach 4*', 'Wszystkie posiłki lokalne', 'Transport klimatyzowanym busem', 'Joga w górach', 'Safari w parku narodowym', 'Zwiedzanie Sigiriya Rock'],
    instructor: 'Julia Jakubowicz',
    image: '/images/retreats/sri-lanka-autumn-2025.jpg',
    earlyBirdPrice: 2400,
    earlyBirdDeadline: new Date(2025, 6, 1)
  },
  {
    id: 5,
    title: 'Retreat w Tatrach - Jesień 2025',
    start: new Date(2025, 9, 15), // October 15, 2025
    end: new Date(2025, 9, 18),   // October 18, 2025
    price: 1100,
    spotsLeft: 10,
    totalSpots: 12,
    status: 'available',
    level: 'wszystkie poziomy',
    location: 'Zakopane, Tatry',
    description: 'Jesienny retreat w polskich górach. Złota jesień w Tatrach z jogą na świeżym powietrzu.',
    includes: ['3 noce w pensjonacie górskim', 'Wszystkie posiłki regionalne', 'Joga na łące', 'Wędrówka na Morskie Oko', 'Sauna i relaks'],
    instructor: 'Julia Jakubowicz',
    image: '/images/retreats/tatry-autumn-2025.jpg',
    earlyBirdPrice: 950,
    earlyBirdDeadline: new Date(2025, 7, 15)
  }
];

export default function BookingCalendar() {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [view, setView] = useState('month');
  const [isCalendarReady, setIsCalendarReady] = useState(false);

  // Initialize calendar after component mounts
  useEffect(() => {
    if (typeof window !== 'undefined' && Calendar && moment) {
      setIsCalendarReady(true);
    }
  }, []);

  // Event style function
  const eventStyleGetter = (event) => {
    let backgroundColor = 'var(--temple-gold)'; // temple color
    let borderColor = 'var(--temple-gold)';
    
    if (event.status === 'full') {
      backgroundColor = 'var(--stone)'; // gray
      borderColor = 'var(--stone)';
    } else if (event.spotsLeft <= 3) {
      backgroundColor = 'var(--temple-gold)'; // amber - almost full
      borderColor = 'var(--temple-gold)';
    }

    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        fontSize: '12px',
        fontWeight: '500'
      }
    };
  };

  // Custom messages in Polish
  const messages = {
    allDay: 'Cały dzień',
    previous: 'Poprzedni',
    next: 'Następny',
    today: 'Dziś',
    month: 'Miesiąc',
    week: 'Tydzień',
    day: 'Dzień',
    agenda: 'Agenda',
    date: 'Data',
    time: 'Czas',
    event: 'Wydarzenie',
    noEventsInRange: 'Brak retreatów w tym okresie',
    showMore: total => `+ ${total} więcej`
  };

  const handleSelectEvent = (event) => {
    setSelectedEvent(event);

    // Track event view in Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'retreat_view', {
        event_category: 'Calendar',
        event_label: event.title,
        value: event.price,
        currency: 'PLN'
      });
    }

    // Track in GTM if available
    if (typeof window !== 'undefined' && window.gtmTrackRetreatView) {
      window.gtmTrackRetreatView(event.title, event.price, event.location);
    }
  };

  const handleBookNow = () => {
    setShowBookingForm(true);
  };

  return (
    <div className="space-y-8">
      {/* Calendar Header */}
      <ScrollReveal className="text-center">
        <h2 className="text-3xl md:text-4xl font-serif text-enterprise-brown mb-4">
          Kalendarz Retreatów
        </h2>
        <p className="text-wood-light text-lg max-w-2xl mx-auto">
          Wybierz termin, który Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej przygodzie
        </p>
      </ScrollReveal>

      {/* Legend */}
      <div className="flex flex-wrap justify-center gap-4 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-enterprise-brown rounded"></div>
          <span>Dostępne miejsca</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-temple-gold rounded"></div>
          <span>Ostatnie miejsca</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-400 rounded"></div>
          <span>Wyprzedane</span>
        </div>
      </div>

      {/* Calendar */}
      <ScrollReveal className="bg-white rectangular shadow-soft p-6 overflow-hidden">
        <div style={{ height: '600px' }}>
          {isCalendarReady && Calendar && momentLocalizer ? (
            <Calendar
              localizer={momentLocalizer(moment)}
              events={retreatEvents}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              eventPropGetter={eventStyleGetter}
              messages={messages}
              views={['month', 'agenda']}
              view={view}
              onView={setView}
              onSelectEvent={handleSelectEvent}
              popup
              showMultiDayTimes
              step={60}
              showAllEvents
              className="custom-calendar"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-4xl mb-4">📅</div>
                <h3 className="text-xl font-serif text-enterprise-brown mb-2">Kalendarz się ładuje...</h3>
                <p className="text-wood-light">Proszę czekać</p>
              </div>
            </div>
          )}
        </div>
      </ScrollReveal>

      {/* Event Details Modal */}
      <AnimatePresence>
        {selectedEvent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.9, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 50 }}
              className="bg-white rectangular p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <h3 className="text-2xl font-serif text-enterprise-brown mb-2">
                  {selectedEvent.title}
                </h3>
                <p className="text-wood-light mb-2">
                  📍 {selectedEvent.location}
                </p>
                <p className="text-wood-light mb-4">
                  📅 {moment(selectedEvent.start).format('DD MMMM')} - {moment(selectedEvent.end).format('DD MMMM YYYY')}
                </p>

                <div className="flex justify-center items-center gap-4 mb-4">
                  <div className="text-center">
                    {selectedEvent.earlyBirdPrice && new Date() < selectedEvent.earlyBirdDeadline ? (
                      <>
                        <span className="text-2xl font-bold text-temple-gold">
                          {selectedEvent.earlyBirdPrice} PLN
                        </span>
                        <p className="text-sm text-temple-gold">Early Bird</p>
                        <span className="text-lg text-gray-500 line-through">
                          {selectedEvent.price} PLN
                        </span>
                      </>
                    ) : (
                      <>
                        <span className="text-3xl font-bold text-enterprise-brown">
                          {selectedEvent.price} PLN
                        </span>
                        <p className="text-sm text-wood-light">Cena regularna</p>
                      </>
                    )}
                  </div>

                  {selectedEvent.status === 'available' && (
                    <div className="text-center">
                      <span className="bg-sanctuary text-enterprise-brown px-3 py-1 rectangular text-sm block">
                        {selectedEvent.spotsLeft}/{selectedEvent.totalSpots} miejsc
                      </span>
                      <p className="text-xs text-wood-light mt-1">dostępnych</p>
                    </div>
                  )}

                  {selectedEvent.status === 'full' && (
                    <span className="bg-red-100 text-red-800 px-3 py-1 rectangular text-sm">
                      {selectedEvent.waitingList ? 'Lista oczekujących' : 'Wyprzedane'}
                    </span>
                  )}
                </div>

                <div className="flex justify-center items-center gap-4 mb-4 text-sm text-wood-light">
                  <span>👩‍🏫 {selectedEvent.instructor}</span>
                  <span>•</span>
                  <span>🧘‍♀️ {selectedEvent.level}</span>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-enterprise-brown mb-2">Opis retreatu:</h4>
                  <p className="text-wood-light">{selectedEvent.description}</p>
                </div>

                <div>
                  <h4 className="font-medium text-enterprise-brown mb-2">W cenie:</h4>
                  <ul className="space-y-1">
                    {selectedEvent.includes.map((item, index) => (
                      <li key={index} className="flex items-center gap-2 text-wood-light">
                        <span className="text-enterprise-brown">✓</span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  {selectedEvent.status === 'available' ? (
                    <>
                      <button
                        onClick={handleBookNow}
                        className="btn-unified-primary flex-1 relative overflow-hidden group"
                      >
                        <span className="relative z-10">
                          {selectedEvent.spotsLeft <= 3 ? '🔥 Ostatnie miejsca!' : 'Zarezerwuj miejsce'}
                        </span>
                        <div className="absolute inset-0 bg-temple-gold transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
                      </button>
                      {selectedEvent.earlyBirdPrice && new Date() < selectedEvent.earlyBirdDeadline && (
                        <div className="text-center text-sm text-temple-gold">
                          ⏰ Early Bird do {moment(selectedEvent.earlyBirdDeadline).format('DD.MM')}
                        </div>
                      )}
                    </>
                  ) : selectedEvent.waitingList ? (
                    <button
                      onClick={() => {
                        // Handle waiting list signup
                        if (typeof window !== 'undefined' && window.gtag) {
                          window.gtag('event', 'waiting_list_signup', {
                            event_category: 'Retreats',
                            event_label: selectedEvent.title
                          });
                        }
                        alert('Funkcja listy oczekujących będzie wkrótce dostępna!');
                      }}
                      className="btn-unified-secondary flex-1"
                    >
                      📝 Dołącz do listy oczekujących
                    </button>
                  ) : (
                    <button
                      disabled
                      className="btn-unified-primary opacity-50 cursor-not-allowed flex-1"
                    >
                      Wyprzedane
                    </button>
                  )}

                  <button
                    onClick={() => setSelectedEvent(null)}
                    className="btn-unified-secondary flex-1"
                  >
                    Zamknij
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Stats */}
      <ScrollReveal className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center p-6 bg-enterprise-brown/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">
            {retreatEvents.filter(e => e.status === 'available').length}
          </div>
          <div className="text-wood-light">Dostępne retreaty</div>
        </div>
        <div className="text-center p-6 bg-golden/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">
            {retreatEvents.reduce((sum, e) => sum + (e.status === 'available' ? e.spotsLeft : 0), 0)}
          </div>
          <div className="text-wood-light">Wolne miejsca</div>
        </div>
        <div className="text-center p-6 bg-sunset/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">7-8</div>
          <div className="text-wood-light">Dni retreatu</div>
        </div>
      </ScrollReveal>
    </div>
  );
}
