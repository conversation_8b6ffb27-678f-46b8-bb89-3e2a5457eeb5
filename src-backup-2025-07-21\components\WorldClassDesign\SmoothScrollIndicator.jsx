'use client';

import React, { useEffect, useState } from 'react';

/**
 * 📊 SMOOTH SCROLL INDICATOR - TOP 1% DESIGN FEATURE
 * Mały wskaźnik postępu przewijania po prawej stronie
 * Inspirowane przez Medium, Linear.app, najlepsze editorial sites
 */
const SmoothScrollIndicator = ({ 
  position = 'right',
  color = '#7C9885',
  width = '2px',
  className = '',
  showOnlyOnScroll = true,
  smoothness = 0.1,
  ...props 
}) => {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isVisible, setIsVisible] = useState(!showOnlyOnScroll);

  useEffect(() => {
    let scrollTimer;
    let currentProgress = 0;
    let targetProgress = 0;

    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
      targetProgress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
      
      setIsScrolling(true);
      if (showOnlyOnScroll) {
        setIsVisible(true);
      }
      
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        setIsScrolling(false);
        if (showOnlyOnScroll && targetProgress < 1) {
          setTimeout(() => setIsVisible(false), 1000);
        }
      }, 150);
    };

    // Smooth interpolation
    const smoothUpdate = () => {
      currentProgress += (targetProgress - currentProgress) * smoothness;
      setScrollProgress(currentProgress);
      
      if (Math.abs(targetProgress - currentProgress) > 0.1) {
        requestAnimationFrame(smoothUpdate);
      }
    };

    const handleScroll = () => {
      updateScrollProgress();
      requestAnimationFrame(smoothUpdate);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Initial call
    updateScrollProgress();
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimer);
    };
  }, [showOnlyOnScroll, smoothness]);

  const positionStyles = {
    right: {
      position: 'fixed',
      top: 0,
      right: '12px',
      height: '100vh',
      width: width,
      zIndex: 50,
    },
    left: {
      position: 'fixed',
      top: 0,
      left: '12px',
      height: '100vh',
      width: width,
      zIndex: 50,
    },
    top: {
      position: 'fixed',
      top: '12px',
      left: 0,
      width: '100vw',
      height: width,
      zIndex: 50,
    },
    bottom: {
      position: 'fixed',
      bottom: '12px',
      left: 0,
      width: '100vw',
      height: width,
      zIndex: 50,
    },
  };

  const progressStyles = {
    right: {
      width: '100%',
      height: `${scrollProgress}%`,
      background: color,
      transition: isScrolling ? 'none' : 'opacity 0.3s ease',
    },
    left: {
      width: '100%',
      height: `${scrollProgress}%`,
      background: color,
      transition: isScrolling ? 'none' : 'opacity 0.3s ease',
    },
    top: {
      width: `${scrollProgress}%`,
      height: '100%',
      background: color,
      transition: isScrolling ? 'none' : 'opacity 0.3s ease',
    },
    bottom: {
      width: `${scrollProgress}%`,
      height: '100%',
      background: color,
      transition: isScrolling ? 'none' : 'opacity 0.3s ease',
    },
  };

  return (
    <div
      className={`scroll-indicator ${className}`}
      style={{
        ...positionStyles[position],
        opacity: isVisible ? 1 : 0,
        transition: 'opacity 0.3s ease',
        pointerEvents: 'none',
      }}
      {...props}
    >
      {/* Background track */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0, 0, 0, 0.05)',
          borderRadius: '1px',
        }}
      />
      
      {/* Progress bar */}
      <div
        style={{
          ...progressStyles[position],
          borderRadius: '1px',
          transformOrigin: position === 'top' || position === 'bottom' ? 'left' : 'top',
          willChange: 'transform',
        }}
      />
      
      {/* Glow effect when scrolling */}
      {isScrolling && (
        <div
          style={{
            position: 'absolute',
            top: position === 'right' || position === 'left' ? `${scrollProgress}%` : 0,
            left: position === 'top' || position === 'bottom' ? `${scrollProgress}%` : 0,
            width: position === 'right' || position === 'left' ? '6px' : '6px',
            height: position === 'right' || position === 'left' ? '6px' : '6px',
            background: color,
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            opacity: 0.6,
            boxShadow: `0 0 12px ${color}`,
            animation: 'pulse 1s ease-in-out infinite',
          }}
        />
      )}
      
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
          50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
        }
        
        @media (max-width: 768px) {
          .scroll-indicator {
            display: none !important;
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .scroll-indicator * {
            animation: none !important;
            transition: none !important;
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(SmoothScrollIndicator);